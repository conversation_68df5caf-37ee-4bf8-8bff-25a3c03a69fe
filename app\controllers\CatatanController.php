<?php
require_once __DIR__ . '/../models/CatatanSiswa.php';
require_once __DIR__ . '/../helpers/Security.php';

class CatatanController {
    private $catatan;
    
    public function __construct() {
        $this->catatan = new CatatanSiswa();
    }
    
    /**
     * Show add catatan form
     */
    public function add($siswaId = null) {
        Security::requireAuth();

        if (!$siswaId) {
            $_SESSION['error'] = 'ID siswa tidak valid';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Get student info
        require_once __DIR__ . '/../models/Siswa.php';
        $siswaModel = new Siswa();
        $siswa = $siswaModel->getById($siswaId);

        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Check if user can access this student
        Security::requireAccessToSiswa($siswaId);

        // Get categories for dropdown and filter by role
        $categories = $this->catatan->getCategoriesGrouped();
        $filteredCategories = Security::filterCatatanCategoriesByRole($categories);

        $data = [
            'title' => 'Tambah Catatan - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'catatan_categories' => $filteredCategories,
            'csrf_token' => Security::generateCSRFToken()
        ];

        $this->view('catatan/add', $data);
    }

    /**
     * Create new catatan
     */
    public function create() {
        Security::requireAuth();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validate CSRF token
                if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Token keamanan tidak valid');
                }
                
                // Get form data
                $data = [
                    'siswa_id' => $_POST['siswa_id'] ?? '',
                    'jenis_catatan' => $_POST['jenis_catatan'] ?? '',
                    'judul_catatan' => $_POST['judul_catatan'] ?? '',
                    'isi_catatan' => $_POST['isi_catatan'] ?? '',
                    'tanggal_catatan' => $_POST['tanggal_catatan'] ?? date('Y-m-d'),
                    'tingkat_prioritas' => $_POST['tingkat_prioritas'] ?? 'sedang',
                    'status_catatan' => $_POST['status_catatan'] ?? 'aktif',
                    'tindak_lanjut' => $_POST['tindak_lanjut'] ?? null,
                    'tanggal_tindak_lanjut' => $_POST['tanggal_tindak_lanjut'] ?? null,
                    'created_by' => $_SESSION['user_id'] ?? 1
                ];
                
                // Validate required fields
                if (empty($data['siswa_id'])) {
                    throw new Exception('ID siswa harus diisi');
                }
                if (empty($data['jenis_catatan'])) {
                    throw new Exception('Jenis catatan harus dipilih');
                }
                if (empty($data['judul_catatan'])) {
                    throw new Exception('Judul catatan harus diisi');
                }
                if (empty($data['isi_catatan'])) {
                    throw new Exception('Isi catatan harus diisi');
                }

                // Check if user can access this student
                Security::requireAccessToSiswa($data['siswa_id']);

                // Check if user can create this type of catatan
                if (!Security::canCreateCatatanType($data['jenis_catatan'])) {
                    throw new Exception('Anda tidak memiliki akses untuk membuat jenis catatan ini');
                }
                
                // Create catatan
                $result = $this->catatan->create($data);
                
                if ($result) {
                    $_SESSION['success'] = 'Catatan berhasil ditambahkan';
                } else {
                    throw new Exception('Gagal menambahkan catatan');
                }
                
            } catch (Exception $e) {
                $_SESSION['error'] = $e->getMessage();
            }
        }
        
        // Redirect back to student detail
        $siswaId = $_POST['siswa_id'] ?? '';
        header('Location: /siswa-app/public/siswa/detail/' . $siswaId);
        exit;
    }
    
    /**
     * Get catatan detail (AJAX)
     */
    public function detail($id) {
        Security::requireAuth();
        
        header('Content-Type: application/json');
        
        try {
            $catatanDetail = $this->catatan->getById($id);
            
            if (!$catatanDetail) {
                throw new Exception('Catatan tidak ditemukan');
            }
            
            echo json_encode([
                'success' => true,
                'catatan' => $catatanDetail
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * Edit catatan
     */
    public function edit($id) {
        Security::requireAuth();
        
        $catatanDetail = $this->catatan->getById($id);
        if (!$catatanDetail) {
            $_SESSION['error'] = 'Catatan tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validate CSRF token
                if (!Security::validateCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Token keamanan tidak valid');
                }
                
                // Get form data
                $data = [
                    'judul_catatan' => $_POST['judul_catatan'] ?? '',
                    'isi_catatan' => $_POST['isi_catatan'] ?? '',
                    'tanggal_catatan' => $_POST['tanggal_catatan'] ?? date('Y-m-d'),
                    'tingkat_prioritas' => $_POST['tingkat_prioritas'] ?? 'sedang',
                    'status_catatan' => $_POST['status_catatan'] ?? 'aktif',
                    'tindak_lanjut' => $_POST['tindak_lanjut'] ?? null,
                    'tanggal_tindak_lanjut' => $_POST['tanggal_tindak_lanjut'] ?? null,
                    'updated_by' => $_SESSION['user_id'] ?? 1
                ];
                
                // Validate required fields
                if (empty($data['judul_catatan'])) {
                    throw new Exception('Judul catatan harus diisi');
                }
                if (empty($data['isi_catatan'])) {
                    throw new Exception('Isi catatan harus diisi');
                }
                
                // Update catatan
                $result = $this->catatan->update($id, $data);
                
                if ($result) {
                    $_SESSION['success'] = 'Catatan berhasil diperbarui';
                    header('Location: /siswa-app/public/siswa/detail/' . $catatanDetail['siswa_id']);
                    exit;
                } else {
                    throw new Exception('Gagal memperbarui catatan');
                }
                
            } catch (Exception $e) {
                $_SESSION['error'] = $e->getMessage();
            }
        }
        
        // Get categories for dropdown
        $categories = $this->catatan->getCategoriesGrouped();
        
        $data = [
            'title' => 'Edit Catatan',
            'catatan' => $catatanDetail,
            'catatan_categories' => $categories,
            'csrf_token' => Security::generateCSRFToken()
        ];
        
        $this->view('catatan/edit', $data);
    }
    
    /**
     * Delete catatan (AJAX)
     */
    public function delete($id) {
        Security::requireAuth();
        
        header('Content-Type: application/json');
        
        try {
            // Get catatan first to check ownership/permission
            $catatanDetail = $this->catatan->getById($id);
            if (!$catatanDetail) {
                throw new Exception('Catatan tidak ditemukan');
            }
            
            // Check if user has permission to delete
            $currentUserId = $_SESSION['user_id'] ?? 0;
            if ($catatanDetail['created_by'] != $currentUserId && !Security::hasRole('admin')) {
                throw new Exception('Anda tidak memiliki izin untuk menghapus catatan ini');
            }
            
            $result = $this->catatan->delete($id);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Catatan berhasil dihapus'
                ]);
            } else {
                throw new Exception('Gagal menghapus catatan');
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * List all catatan for a student
     */
    public function index($siswaId = null) {
        Security::requireAuth();
        
        if (!$siswaId) {
            $_SESSION['error'] = 'ID siswa tidak valid';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        // Get student info
        require_once __DIR__ . '/../models/Siswa.php';
        $siswaModel = new Siswa();
        $siswa = $siswaModel->getById($siswaId);
        
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }
        
        // Get catatan
        $catatanGrouped = $this->catatan->getGroupedBySiswaId($siswaId);
        $categories = $this->catatan->getCategoriesGrouped();
        $statistics = $this->catatan->getStatistics($siswaId);
        
        $data = [
            'title' => 'Catatan Siswa - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'catatan_grouped' => $catatanGrouped,
            'catatan_categories' => $categories,
            'statistics' => $statistics,
            'csrf_token' => Security::generateCSRFToken()
        ];
        
        $this->view('catatan/index', $data);
    }
    
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
    
    private function getById($id) {
        try {
            return $this->catatan->db->fetch("
                SELECT c.*, k.nama_kategori, k.warna_badge, k.icon_class, u.username as created_by_name
                FROM catatan_siswa c
                LEFT JOIN kategori_catatan k ON c.jenis_catatan = k.kode_kategori
                LEFT JOIN users u ON c.created_by = u.id
                WHERE c.id = ?
            ", [$id]);
        } catch (Exception $e) {
            return null;
        }
    }
}
?>
