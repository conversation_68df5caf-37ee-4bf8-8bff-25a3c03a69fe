<?php
require_once __DIR__ . '/../models/Berkas.php';
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../helpers/Security.php';

class BerkasController {
    private $berkasModel;
    private $siswaModel;

    public function __construct() {
        $this->berkasModel = new Berkas();
        $this->siswaModel = new Siswa();
    }

    /**
     * Show all files for a student
     */
    public function index($siswaId) {
        Security::requireAuth();
        
        // Get student data
        $siswa = $this->siswaModel->getById($siswaId);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Get all files for this student
        $berkas = $this->berkasModel->getBySiswaId($siswaId);
        
        $data = [
            'title' => 'Berkas Siswa - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'berkas' => $berkas
        ];

        $this->view('berkas/index', $data);
    }

    /**
     * View/Download file
     */
    public function viewFile($berkasId) {
        Security::requireAuth();

        try {
            $berkas = $this->berkasModel->getById($berkasId);
            if (!$berkas) {
                http_response_code(404);
                echo "File tidak ditemukan";
                exit;
            }

            // Check if user can access this student's files
            if (!Security::canAccessSiswa($berkas['siswa_id'])) {
                http_response_code(403);
                echo "Access denied";
                exit;
            }

            // Determine file path
            $filePath = $this->getFilePath($berkas);

            if (!file_exists($filePath)) {
                http_response_code(404);
                echo "File fisik tidak ditemukan: " . htmlspecialchars($berkas['nama_berkas']);
                exit;
            }

            // Set appropriate headers
            $this->setFileHeaders($berkas, $filePath);

            // Output file
            readfile($filePath);
            exit;

        } catch (Exception $e) {
            http_response_code(500);
            echo "Error: " . htmlspecialchars($e->getMessage());
            exit;
        }
    }

    /**
     * Download file (force download)
     */
    public function download($berkasId) {
        Security::requireAuth();

        try {
            $berkas = $this->berkasModel->getById($berkasId);
            if (!$berkas) {
                http_response_code(404);
                echo "File tidak ditemukan";
                exit;
            }

            // Check if user can access this student's files
            if (!Security::canAccessSiswa($berkas['siswa_id'])) {
                http_response_code(403);
                echo "Access denied";
                exit;
            }

            // Determine file path
            $filePath = $this->getFilePath($berkas);

            if (!file_exists($filePath)) {
                http_response_code(404);
                echo "File fisik tidak ditemukan";
                exit;
            }

            // Force download headers
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $berkas['nama_file_asli'] . '"');
            header('Content-Length: ' . filesize($filePath));
            header('Cache-Control: must-revalidate');
            header('Pragma: public');

            // Output file
            readfile($filePath);
            exit;

        } catch (Exception $e) {
            http_response_code(500);
            echo "Error: " . htmlspecialchars($e->getMessage());
            exit;
        }
    }

    /**
     * Get file path from berkas data
     */
    private function getFilePath($berkas) {
        // Try different possible paths
        $possiblePaths = [
            __DIR__ . '/../../' . $berkas['file_path'],
            __DIR__ . '/../../uploads/berkas/' . $berkas['nama_file_sistem'],
            __DIR__ . '/../../uploads/berkas/' . $berkas['nama_file_asli'],
            __DIR__ . '/../../uploads/berkas/' . $berkas['jenis_berkas'] . '/' . $berkas['nama_file_sistem'],
            __DIR__ . '/../../uploads/berkas/' . $berkas['jenis_berkas'] . '/' . $berkas['nama_file_asli']
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // If not found, return the primary path for error reporting
        return __DIR__ . '/../../' . $berkas['file_path'];
    }

    /**
     * Set appropriate headers for file viewing
     */
    private function setFileHeaders($berkas, $filePath) {
        $mimeType = $berkas['mime_type'] ?? 'application/octet-stream';
        $fileName = $berkas['nama_file_asli'] ?? 'file';

        // Set MIME type
        header('Content-Type: ' . $mimeType);

        // For PDFs and images, display inline; for others, download
        if (strpos($mimeType, 'image/') === 0 || $mimeType === 'application/pdf') {
            header('Content-Disposition: inline; filename="' . $fileName . '"');
        } else {
            header('Content-Disposition: attachment; filename="' . $fileName . '"');
        }

        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: public, max-age=3600');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($filePath)) . ' GMT');
    }

    /**
     * Show files by category
     */
    public function category($siswaId, $category) {
        Security::requireAuth();
        
        // Get student data
        $siswa = $this->siswaModel->getById($siswaId);
        if (!$siswa) {
            $_SESSION['error'] = 'Data siswa tidak ditemukan';
            header('Location: /siswa-app/public/siswa');
            exit;
        }

        // Get files by category
        $berkas = $this->berkasModel->getBySiswaIdAndCategory($siswaId, $category);
        
        $data = [
            'title' => 'Berkas ' . ucfirst($category) . ' - ' . $siswa['nama_lengkap'],
            'siswa' => $siswa,
            'berkas' => $berkas,
            'category' => $category
        ];

        $this->view('berkas/category', $data);
    }

    /**
     * Render view
     */
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>
