<?php

class Security {
    
    /**
     * Generate CSRF Token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * Validate CSRF token
     */
    public static function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token']) || empty($token)) {
            return false;
        }

        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Verify CSRF Token
     */
    public static function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate email
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate password strength
     */
    public static function validatePassword($password) {
        // Minimum 8 characters, at least one letter and one number
        return strlen($password) >= 8 && preg_match('/^(?=.*[A-Za-z])(?=.*\d)/', $password);
    }
    
    /**
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate secure random string
     */
    public static function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Validate file upload
     */
    public static function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880) { // 5MB default
        $errors = [];
        
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $errors[] = 'File tidak valid atau tidak ditemukan';
            return $errors;
        }
        
        // Check file size
        if ($file['size'] > $maxSize) {
            $errors[] = 'Ukuran file terlalu besar. Maksimal ' . ($maxSize / 1024 / 1024) . 'MB';
        }
        
        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!empty($allowedTypes) && !in_array($mimeType, $allowedTypes)) {
            $errors[] = 'Tipe file tidak diizinkan. Hanya: ' . implode(', ', $allowedTypes);
        }
        
        // Check for malicious content
        $fileContent = file_get_contents($file['tmp_name']);
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $fileContent)) {
                $errors[] = 'File mengandung konten yang tidak diizinkan';
                break;
            }
        }
        
        return $errors;
    }
    
    /**
     * Generate secure filename
     */
    public static function generateSecureFilename($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // Sanitize filename
        $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
        $filename = substr($filename, 0, 50); // Limit length
        
        // Add timestamp and random string
        $secureFilename = $filename . '_' . time() . '_' . self::generateRandomString(8) . '.' . $extension;
        
        return $secureFilename;
    }
    
    /**
     * Calculate file hash
     */
    public static function calculateFileHash($filePath) {
        return hash_file('sha256', $filePath);
    }
    
    /**
     * Rate limiting check
     */
    public static function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 300) { // 5 attempts in 5 minutes
        if (!isset($_SESSION['rate_limit'])) {
            $_SESSION['rate_limit'] = [];
        }
        
        $now = time();
        $key = md5($identifier);
        
        // Clean old entries
        if (isset($_SESSION['rate_limit'][$key])) {
            $_SESSION['rate_limit'][$key] = array_filter(
                $_SESSION['rate_limit'][$key],
                function($timestamp) use ($now, $timeWindow) {
                    return ($now - $timestamp) < $timeWindow;
                }
            );
        } else {
            $_SESSION['rate_limit'][$key] = [];
        }
        
        // Check if limit exceeded
        if (count($_SESSION['rate_limit'][$key]) >= $maxAttempts) {
            return false;
        }
        
        // Add current attempt
        $_SESSION['rate_limit'][$key][] = $now;
        return true;
    }
    
    /**
     * Log security event
     */
    public static function logSecurityEvent($event, $details = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'details' => $details
        ];
        
        $logFile = __DIR__ . '/../../logs/security.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Check if user has required role
     */
    public static function hasRole($requiredRole) {
        if (!self::isAuthenticated()) {
            return false;
        }
        
        $userRole = $_SESSION['user_role'] ?? '';
        
        // Admin has access to everything
        if ($userRole === 'admin') {
            return true;
        }
        
        // Check specific role
        if (is_array($requiredRole)) {
            return in_array($userRole, $requiredRole);
        }
        
        return $userRole === $requiredRole;
    }
    
    /**
     * Redirect if not authenticated
     */
    public static function requireAuth($redirectTo = '/siswa-app/public/login') {
        if (!self::isAuthenticated()) {
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
            header('Location: ' . $redirectTo);
            exit;
        }
    }

    /**
     * Redirect if insufficient role
     */
    public static function requireRole($requiredRole, $redirectTo = '/siswa-app/public/unauthorized') {
        if (!self::hasRole($requiredRole)) {
            header('Location: ' . $redirectTo);
            exit;
        }
    }

    /**
     * Check if current user is wali kelas
     */
    public static function isWaliKelas() {
        return self::isAuthenticated() && ($_SESSION['user_role'] ?? '') === 'wali_kelas';
    }

    /**
     * Check if current user is pamong
     */
    public static function isPamong() {
        return self::isAuthenticated() && strpos(($_SESSION['user_role'] ?? ''), 'pamong_') === 0;
    }

    /**
     * Check if current user is staff
     */
    public static function isStaff() {
        return self::isAuthenticated() && ($_SESSION['user_role'] ?? '') === 'staff';
    }

    /**
     * Get pamong type (mp, mt, mm, mu)
     */
    public static function getPamongType() {
        if (!self::isPamong()) {
            return null;
        }

        $role = $_SESSION['user_role'] ?? '';
        return str_replace('pamong_', '', $role);
    }

    /**
     * Get kelas IDs that current wali kelas can access
     */
    public static function getWaliKelasAccessibleKelas() {
        if (!self::isWaliKelas()) {
            return [];
        }

        try {
            require_once __DIR__ . '/../models/Database.php';
            $db = new Database();

            // First try user_kelas_mapping table
            $kelasIds = $db->fetchAll("
                SELECT kelas_id
                FROM user_kelas_mapping
                WHERE user_id = ? AND role_type = 'wali_kelas' AND is_active = 1
            ", [$_SESSION['user_id']]);

            if (!empty($kelasIds)) {
                return array_column($kelasIds, 'kelas_id');
            }

            // Fallback: check direct assignment in kelas table
            $kelasIds = $db->fetchAll("
                SELECT id as kelas_id
                FROM kelas
                WHERE wali_kelas_id = ? AND is_active = 1
            ", [$_SESSION['user_id']]);

            return array_column($kelasIds, 'kelas_id');
        } catch (Exception $e) {
            error_log("Error getting wali kelas accessible kelas: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get wali kelas type based on assigned kelas
     */
    public static function getWaliKelasType() {
        if (!self::isWaliKelas()) {
            return null;
        }

        try {
            require_once __DIR__ . '/../models/Database.php';
            $db = new Database();

            $kelas = $db->fetch("
                SELECT k.nama_kelas, k.tingkat
                FROM kelas k
                WHERE k.wali_kelas_id = ? AND k.is_active = 1
                LIMIT 1
            ", [$_SESSION['user_id']]);

            if ($kelas) {
                return [
                    'tingkat' => $kelas['tingkat'],
                    'nama_kelas' => $kelas['nama_kelas']
                ];
            }

            return null;
        } catch (Exception $e) {
            error_log("Error getting wali kelas type: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if wali kelas can access specific siswa
     */
    public static function canWaliKelasAccessSiswa($siswaId) {
        if (!self::isWaliKelas()) {
            return false;
        }

        try {
            require_once __DIR__ . '/../models/Database.php';
            $db = new Database();

            $result = $db->fetch("
                SELECT COUNT(*) as count
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                WHERE s.id_siswa = ?
                AND k.wali_kelas_id = ?
                AND s.status_siswa = 'aktif'
                AND k.is_active = 1
            ", [$siswaId, $_SESSION['user_id']]);

            return $result && $result['count'] > 0;
        } catch (Exception $e) {
            error_log("Error checking wali kelas access to siswa: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if pamong can access specific siswa
     */
    public static function canPamongAccessSiswa($siswaId) {
        if (!self::isPamong()) {
            return false;
        }

        try {
            require_once __DIR__ . '/../models/Database.php';
            $db = new Database();

            $pamongType = self::getPamongType();
            $allowedTingkat = [];

            switch ($pamongType) {
                case 'mp':
                    $allowedTingkat = ['KPP'];
                    break;
                case 'mt':
                    $allowedTingkat = ['X'];
                    break;
                case 'mm':
                    $allowedTingkat = ['XI'];
                    break;
                case 'mu':
                    $allowedTingkat = ['XII', 'KPA'];
                    break;
                default:
                    return false;
            }

            $placeholders = str_repeat('?,', count($allowedTingkat) - 1) . '?';
            $params = array_merge([$siswaId], $allowedTingkat);

            $result = $db->fetch("
                SELECT COUNT(*) as count
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                WHERE s.id_siswa = ?
                AND k.tingkat IN ($placeholders)
                AND s.status_siswa = 'aktif'
                AND k.is_active = 1
            ", $params);

            return $result && $result['count'] > 0;
        } catch (Exception $e) {
            error_log("Error checking pamong access to siswa: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user can access specific siswa (unified function)
     */
    public static function canAccessSiswa($siswaId) {
        if (!self::isAuthenticated()) {
            return false;
        }

        $userRole = $_SESSION['user_role'] ?? '';

        // Admin can access all
        if ($userRole === 'admin') {
            return true;
        }

        // Staff can access all (read-only)
        if (self::isStaff()) {
            return true;
        }

        // Wali kelas can access their students
        if (self::isWaliKelas()) {
            return self::canWaliKelasAccessSiswa($siswaId);
        }

        // Pamong can access students by tingkat
        if (self::isPamong()) {
            return self::canPamongAccessSiswa($siswaId);
        }

        return false;
    }

    /**
     * Check if user can create specific type of catatan
     */
    public static function canCreateCatatanType($jenisCatatan) {
        if (!self::isAuthenticated()) {
            return false;
        }

        $userRole = $_SESSION['user_role'] ?? '';

        // Admin can create all types
        if ($userRole === 'admin') {
            return true;
        }

        // Define role-based access for catatan types
        $roleAccess = [
            // Pamong access by tingkat
            'pamong_mp' => ['pamong'],  // MP can create pamong_mp catatan
            'pamong_mt' => ['pamong'],  // MT can create pamong_mt catatan
            'pamong_mm' => ['pamong'],  // MM can create pamong_mm catatan
            'pamong_mu' => ['pamong'],  // MU can create pamong_mu catatan

            // Wali kelas access by tingkat
            'wali_kpp' => ['wali_kelas'],
            'wali_x' => ['wali_kelas'],
            'wali_xi' => ['wali_kelas'],
            'wali_xii' => ['wali_kelas'],
            'wali_kpa' => ['wali_kelas'],

            // BK access
            'bk_konseling' => ['bk', 'admin'],
            'bk_pelanggaran' => ['bk', 'admin'],
            'bk_prestasi' => ['bk', 'admin'],
            'bk_lainnya' => ['bk', 'admin'],

            // General categories
            'akademik' => ['pamong', 'wali_kelas', 'admin'],
            'prestasi' => ['pamong', 'wali_kelas', 'bk', 'admin'],
            'pelanggaran' => ['pamong', 'wali_kelas', 'bk', 'admin'],
            'kesehatan' => ['pamong', 'wali_kelas', 'admin'],
            'kehadiran' => ['pamong', 'wali_kelas', 'admin'],
            'umum' => ['pamong', 'wali_kelas', 'bk', 'admin']
        ];

        // Check if user role is allowed for this catatan type
        if (isset($roleAccess[$jenisCatatan])) {
            return in_array($userRole, $roleAccess[$jenisCatatan]);
        }

        // For pamong, check specific tingkat access
        if ($userRole === 'pamong') {
            $pamongTingkat = $_SESSION['pamong_tingkat'] ?? '';

            // Map pamong tingkat to allowed catatan types
            $pamongAccess = [
                'MP' => ['pamong_mp', 'wali_kpp'],
                'MT' => ['pamong_mt', 'wali_x'],
                'MM' => ['pamong_mm', 'wali_xi'],
                'MU' => ['pamong_mu', 'wali_xii', 'wali_kpa']
            ];

            if (isset($pamongAccess[$pamongTingkat])) {
                return in_array($jenisCatatan, $pamongAccess[$pamongTingkat]);
            }
        }

        // For wali kelas, check if they can create catatan for their specific class
        if ($userRole === 'wali_kelas') {
            // Get wali kelas info from database
            $waliKelasInfo = self::getWaliKelasType();

            if ($waliKelasInfo) {
                $tingkat = $waliKelasInfo['tingkat'];
                $namaKelas = $waliKelasInfo['nama_kelas'];

                // Map kelas name to catatan type
                $kelasToCategory = [
                    'KPP A' => 'wali_kpp_a',
                    'KPP B' => 'wali_kpp_b',
                    'KPP C' => 'wali_kpp_c',
                    'X-1' => 'wali_x_1',
                    'X-2' => 'wali_x_2',
                    'XI-1' => 'wali_xi_1',
                    'XI-2' => 'wali_xi_2',
                    'XII-1' => 'wali_xii_1',
                    'XII-2' => 'wali_xii_2',
                    'KPA' => 'wali_kpa'
                ];

                // Allow wali kelas to create catatan for their specific class
                if (isset($kelasToCategory[$namaKelas]) && $jenisCatatan === $kelasToCategory[$namaKelas]) {
                    return true;
                }

                // Also allow general categories
                $generalCategories = ['akademik', 'prestasi', 'umum', 'kehadiran'];
                if (in_array($jenisCatatan, $generalCategories)) {
                    return true;
                }
            }

            return false;
        }

        // Default: allow general categories for authenticated users
        $generalCategories = ['akademik', 'prestasi', 'umum'];
        return in_array($jenisCatatan, $generalCategories);
    }

    /**
     * Filter catatan categories based on user role
     */
    public static function filterCatatanCategoriesByRole($categories) {
        if (!self::isAuthenticated()) {
            return [];
        }

        $userRole = $_SESSION['user_role'] ?? '';

        // Admin can see all categories
        if ($userRole === 'admin') {
            return $categories;
        }

        $filteredCategories = [];

        foreach ($categories as $groupName => $groupCategories) {
            $filteredGroup = [];

            foreach ($groupCategories as $category) {
                if (self::canCreateCatatanType($category['kode_kategori'])) {
                    $filteredGroup[] = $category;
                }
            }

            if (!empty($filteredGroup)) {
                $filteredCategories[$groupName] = $filteredGroup;
            }
        }

        return $filteredCategories;
    }

    /**
     * Check if user can edit siswa data
     */
    public static function canEditSiswa($siswaId = null) {
        if (!self::isAuthenticated()) {
            return false;
        }

        $userRole = $_SESSION['user_role'] ?? '';

        // Admin can edit all
        if ($userRole === 'admin') {
            return true;
        }

        // Staff cannot edit (read-only)
        if (self::isStaff()) {
            return false;
        }

        // Wali kelas can edit their students
        if (self::isWaliKelas()) {
            return $siswaId ? self::canWaliKelasAccessSiswa($siswaId) : true;
        }

        // Pamong can edit students in their tingkat
        if (self::isPamong()) {
            return $siswaId ? self::canPamongAccessSiswa($siswaId) : true;
        }

        return false;
    }

    /**
     * Require access to specific siswa
     */
    public static function requireAccessToSiswa($siswaId, $redirectTo = '/siswa-app/public/unauthorized') {
        if (!self::canAccessSiswa($siswaId)) {
            header('Location: ' . $redirectTo);
            exit;
        }
    }

    /**
     * Require edit permission for siswa
     */
    public static function requireEditSiswa($siswaId = null, $redirectTo = '/siswa-app/public/unauthorized') {
        if (!self::canEditSiswa($siswaId)) {
            header('Location: ' . $redirectTo);
            exit;
        }
    }

    /**
     * Require wali kelas access to specific siswa (legacy)
     */
    public static function requireWaliKelasAccessToSiswa($siswaId, $redirectTo = '/siswa-app/public/unauthorized') {
        self::requireAccessToSiswa($siswaId, $redirectTo);
    }


}
