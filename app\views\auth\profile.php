<?php
require_once __DIR__ . '/../layouts/header.php';
require_once __DIR__ . '/../components/elegant-alerts.php';
?>

<div class="container-fluid py-4">
    <!-- Enhanced Alert Messages -->
    <?= showSessionAlerts() ?>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/siswa-app/public/dashboard">
                    <i class="bi bi-house"></i> Dashboard
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="bi bi-person-circle"></i> Profil Saya
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-circle text-primary"></i>
                        Profil Saya
                    </h1>
                    <p class="text-muted mb-0">
                        Kelola informasi profil dan keamanan akun Anda
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Profile Info -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="avatar-circle mx-auto mb-3">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <h5 class="card-title"><?= htmlspecialchars($user['nama_lengkap'] ?? 'User') ?></h5>
                        <p class="text-muted mb-1">
                            <i class="bi bi-at"></i> <?= htmlspecialchars($user['username'] ?? '') ?>
                        </p>
                        <span class="badge bg-primary">
                            <i class="bi bi-shield-check"></i>
                            <?= ucfirst($user['role'] ?? 'User') ?>
                        </span>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-primary mb-0">
                                    <?= date('d M Y', strtotime($user['created_at'] ?? 'now')) ?>
                                </h6>
                                <small class="text-muted">Bergabung</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-success mb-0">
                                <?= date('d M Y', strtotime($user['last_login'] ?? 'now')) ?>
                            </h6>
                            <small class="text-muted">Login Terakhir</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pencil-square"></i>
                        Edit Profil
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/siswa-app/public/auth/update-profile">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <!-- Basic Info -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person"></i> Username
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= htmlspecialchars($user['username'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="nama_lengkap" class="form-label">
                                    <i class="bi bi-card-text"></i> Nama Lengkap
                                </label>
                                <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" 
                                       value="<?= htmlspecialchars($user['nama_lengkap'] ?? '') ?>" required>
                            </div>
                        </div>

                        <!-- Contact Info -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope"></i> Email
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= htmlspecialchars($user['email'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="no_telepon" class="form-label">
                                    <i class="bi bi-telephone"></i> No. Telepon
                                </label>
                                <input type="tel" class="form-control" id="no_telepon" name="no_telepon" 
                                       value="<?= htmlspecialchars($user['no_telepon'] ?? '') ?>">
                            </div>
                        </div>

                        <!-- Role Info (Read Only) -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="bi bi-shield"></i> Role
                                </label>
                                <input type="text" class="form-control" 
                                       value="<?= ucfirst($user['role'] ?? 'User') ?>" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="bi bi-check-circle"></i> Status
                                </label>
                                <input type="text" class="form-control" 
                                       value="<?= ($user['is_active'] ?? 1) ? 'Aktif' : 'Tidak Aktif' ?>" readonly>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Profil
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Change Password -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-key"></i>
                        Ubah Password
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/siswa-app/public/auth/change-password">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="bi bi-lock"></i> Password Saat Ini
                            </label>
                            <input type="password" class="form-control" id="current_password" 
                                   name="current_password" required>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="new_password" class="form-label">
                                    <i class="bi bi-key"></i> Password Baru
                                </label>
                                <input type="password" class="form-control" id="new_password" 
                                       name="new_password" required>
                            </div>
                            <div class="col-md-6">
                                <label for="confirm_password" class="form-label">
                                    <i class="bi bi-key-fill"></i> Konfirmasi Password
                                </label>
                                <input type="password" class="form-control" id="confirm_password" 
                                       name="confirm_password" required>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-warning">
                                <i class="bi bi-shield-lock"></i> Ubah Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Alert Styles -->
<?= getAlertStyles() ?>
<?= getAlertScripts() ?>

<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.card {
    border: none;
    border-radius: 12px;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
}

.breadcrumb {
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #0d6efd;
}

.breadcrumb-item.active {
    color: #495057;
}

.badge {
    font-size: 0.875rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Password tidak cocok');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    if (newPassword && confirmPassword) {
        newPassword.addEventListener('input', validatePassword);
        confirmPassword.addEventListener('input', validatePassword);
    }

    // Form submission handling
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            
            if (form.action.includes('change-password')) {
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Mengubah Password...';
            } else {
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Menyimpan...';
            }
        });
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
