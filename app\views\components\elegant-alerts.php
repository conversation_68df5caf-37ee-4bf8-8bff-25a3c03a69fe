<?php
/**
 * Elegant Alert Component
 * Modern, beautiful alert system for better user experience
 */

function renderElegantAlert($type, $title, $message, $actions = [], $autoHide = true) {
    $alertId = 'alert_' . uniqid();
    
    $iconMap = [
        'error' => 'bi-exclamation-triangle-fill',
        'success' => 'bi-check-circle-fill',
        'warning' => 'bi-exclamation-triangle-fill',
        'info' => 'bi-info-circle-fill'
    ];
    
    $titleMap = [
        'error' => 'Oops! <PERSON>r<PERSON><PERSON>',
        'success' => 'Berhasil!',
        'warning' => 'Perhatian!',
        'info' => 'Informasi'
    ];
    
    $icon = $iconMap[$type] ?? 'bi-info-circle-fill';
    $alertTitle = $title ?: ($titleMap[$type] ?? 'Notifikasi');
    
    ob_start();
    ?>
    <div class="elegant-alert alert-<?= $type ?>" id="<?= $alertId ?>" <?= $autoHide ? 'data-auto-hide="true"' : '' ?>>
        <div class="alert-content">
            <div class="alert-icon">
                <i class="bi <?= $icon ?>"></i>
            </div>
            <div class="alert-body">
                <div class="alert-title"><?= htmlspecialchars($alertTitle) ?></div>
                <div class="alert-message"><?= htmlspecialchars($message) ?></div>
                <div class="alert-actions">
                    <?php foreach ($actions as $action): ?>
                        <button type="button" 
                                class="<?= $action['class'] ?? 'btn-action' ?>" 
                                onclick="<?= $action['onclick'] ?? '' ?>">
                            <?php if (!empty($action['icon'])): ?>
                                <i class="bi <?= $action['icon'] ?>"></i>
                            <?php endif; ?>
                            <?= htmlspecialchars($action['text']) ?>
                        </button>
                    <?php endforeach; ?>
                    <button type="button" class="btn-dismiss" onclick="dismissAlert('<?= $alertId ?>')">
                        <i class="bi bi-x"></i> Tutup
                    </button>
                </div>
            </div>
        </div>
        <?php if ($autoHide): ?>
            <div class="alert-progress"></div>
        <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
}

function showSessionAlerts() {
    $alerts = [];
    
    if (isset($_SESSION['error'])) {
        $actions = [
            [
                'class' => 'btn-retry',
                'icon' => 'bi-arrow-clockwise',
                'text' => 'Coba Lagi',
                'onclick' => 'location.reload()'
            ]
        ];
        $alerts[] = renderElegantAlert('error', '', $_SESSION['error'], $actions);
        unset($_SESSION['error']);
    }
    
    if (isset($_SESSION['success'])) {
        $alerts[] = renderElegantAlert('success', '', $_SESSION['success']);
        unset($_SESSION['success']);
    }
    
    if (isset($_SESSION['warning'])) {
        $alerts[] = renderElegantAlert('warning', '', $_SESSION['warning']);
        unset($_SESSION['warning']);
    }
    
    if (isset($_SESSION['info'])) {
        $alerts[] = renderElegantAlert('info', '', $_SESSION['info']);
        unset($_SESSION['info']);
    }
    
    return implode("\n", $alerts);
}

function getAlertStyles() {
    ob_start();
    ?>
    <style>
    /* Elegant Alert Styles */
    .elegant-alert {
        position: relative;
        margin: 1rem 0;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        animation: slideInDown 0.5s ease-out;
        backdrop-filter: blur(10px);
    }

    .elegant-alert.alert-error {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        border-left: 4px solid #dc3545;
    }

    .elegant-alert.alert-success {
        background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        border-left: 4px solid #28a745;
    }

    .elegant-alert.alert-warning {
        background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
        border-left: 4px solid #ffc107;
    }

    .elegant-alert.alert-info {
        background: linear-gradient(135deg, #74c0fc 0%, #339af0 100%);
        border-left: 4px solid #17a2b8;
    }

    .alert-content {
        display: flex;
        align-items: flex-start;
        padding: 1.25rem;
        color: white;
    }

    .alert-icon {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        margin-right: 1rem;
        font-size: 1.5rem;
    }

    .alert-body {
        flex: 1;
        min-width: 0;
    }

    .alert-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: white;
    }

    .alert-message {
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1rem;
        color: rgba(255, 255, 255, 0.95);
    }

    .alert-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn-retry, .btn-dismiss, .btn-action {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-retry:hover, .btn-dismiss:hover, .btn-action:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .alert-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: rgba(255, 255, 255, 0.3);
        animation: progressBar 8s linear forwards;
    }

    @keyframes progressBar {
        from { width: 100%; }
        to { width: 0%; }
    }

    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideOutUp {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-30px);
        }
    }

    .elegant-alert.dismissing {
        animation: slideOutUp 0.3s ease-in forwards;
    }

    /* Responsive Alert */
    @media (max-width: 768px) {
        .alert-content {
            padding: 1rem;
        }
        
        .alert-icon {
            width: 40px;
            height: 40px;
            font-size: 1.25rem;
            margin-right: 0.75rem;
        }
        
        .alert-title {
            font-size: 1rem;
        }
        
        .alert-message {
            font-size: 0.875rem;
        }
        
        .alert-actions {
            flex-direction: column;
        }
        
        .btn-retry, .btn-dismiss, .btn-action {
            width: 100%;
            justify-content: center;
        }
    }
    </style>
    <?php
    return ob_get_clean();
}

function getAlertScripts() {
    ob_start();
    ?>
    <script>
    // Enhanced Alert System
    function dismissAlert(alertId) {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.classList.add('dismissing');
            setTimeout(() => {
                alert.remove();
            }, 300);
        }
    }

    // Enhanced notification system
    function showNotification(type, title, message, actions = []) {
        const alertId = 'notification_' + Date.now();
        const iconMap = {
            'error': 'bi-exclamation-triangle-fill',
            'success': 'bi-check-circle-fill',
            'warning': 'bi-exclamation-triangle-fill',
            'info': 'bi-info-circle-fill'
        };
        
        const actionsHtml = actions.map(action => 
            `<button type="button" class="${action.class || 'btn-action'}" onclick="${action.onclick || ''}">
                ${action.icon ? `<i class="bi ${action.icon}"></i>` : ''} ${action.text}
            </button>`
        ).join('');
        
        const alertHtml = `
            <div class="elegant-alert alert-${type}" id="${alertId}">
                <div class="alert-content">
                    <div class="alert-icon">
                        <i class="bi ${iconMap[type]}"></i>
                    </div>
                    <div class="alert-body">
                        <div class="alert-title">${title}</div>
                        <div class="alert-message">${message}</div>
                        <div class="alert-actions">
                            ${actionsHtml}
                            <button type="button" class="btn-dismiss" onclick="dismissAlert('${alertId}')">
                                <i class="bi bi-x"></i> Tutup
                            </button>
                        </div>
                    </div>
                </div>
                <div class="alert-progress"></div>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid') || document.body;
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = alertHtml;
        container.insertBefore(tempDiv.firstElementChild, container.firstElementChild);
        
        // Auto-dismiss after 8 seconds
        setTimeout(() => {
            dismissAlert(alertId);
        }, 8000);
    }

    // Auto-dismiss alerts on page load
    document.addEventListener('DOMContentLoaded', function() {
        const alerts = document.querySelectorAll('.elegant-alert[data-auto-hide="true"]');
        alerts.forEach(alert => {
            setTimeout(() => {
                if (alert && !alert.classList.contains('dismissing')) {
                    dismissAlert(alert.id);
                }
            }, 8000);
        });
    });
    </script>
    <?php
    return ob_get_clean();
}
?>
