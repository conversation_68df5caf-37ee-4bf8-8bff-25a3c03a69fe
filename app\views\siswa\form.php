<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-person-plus text-primary"></i>
                        <?= $title ?? 'Tambah Siswa Baru' ?>
                    </h1>
                    <p class="text-muted mb-0">Lengkapi form di bawah untuk menambahkan siswa baru</p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Error!</strong> <?= htmlspecialchars($_SESSION['error']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['error']); endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i>
        <strong>Berhasil!</strong> <?= htmlspecialchars($_SESSION['success']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['success']); endif; ?>

    <!-- Main Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-person-plus"></i>
                        Form Tambah Siswa
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/siswa-app/public/siswa/create" id="formTambahSiswa" novalidate>
                        <!-- CSRF Token -->
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?? '' ?>">

                        <!-- Basic Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="bi bi-person-circle text-primary"></i>
                                Informasi Dasar
                            </h6>
                            <hr class="section-divider">

                            <div class="row">
                                <!-- Nama Lengkap -->
                                <div class="col-md-12 mb-3">
                                    <label for="nama" class="form-label required">
                                        <i class="bi bi-person"></i>
                                        Nama Lengkap
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="nama"
                                           name="nama"
                                           placeholder="Masukkan nama lengkap siswa"
                                           required
                                           autocomplete="name">
                                    <div class="invalid-feedback">
                                        Nama lengkap harus diisi
                                    </div>
                                    <small class="form-text text-muted">
                                        Masukkan nama lengkap sesuai dokumen resmi
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Academic Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="bi bi-building text-success"></i>
                                Informasi Akademik
                            </h6>
                            <hr class="section-divider">

                            <div class="row">
                                <!-- Kelas -->
                                <div class="col-md-12 mb-3">
                                    <label for="kelas_id" class="form-label required">
                                        <i class="bi bi-building"></i>
                                        Kelas
                                    </label>
                                    <select class="form-control" id="kelas_id" name="kelas_id" required>
                                        <option value="">-- Pilih Kelas --</option>
                                        <?php if (!empty($kelas)): ?>
                                            <?php foreach ($kelas as $k): ?>
                                            <option value="<?= $k['id'] ?>">
                                                <?= htmlspecialchars($k['tingkat'] . ' - ' . $k['nama_kelas']) ?>
                                                <?php if (!empty($k['jurusan'])): ?>
                                                    (<?= htmlspecialchars($k['jurusan']) ?>)
                                                <?php endif; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <option value="" disabled>Tidak ada kelas yang tersedia</option>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Kelas harus dipilih
                                    </div>
                                    <small class="form-text text-muted">
                                        Pilih kelas sesuai dengan tingkat dan jurusan siswa
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <hr>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        Field dengan tanda <span class="text-danger">*</span> wajib diisi
                                    </small>
                                </div>
                                <div>
                                    <a href="/siswa-app/public/siswa" class="btn btn-secondary mr-2">
                                        <i class="bi bi-x-circle"></i>
                                        Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="btnSubmit">
                                        <i class="bi bi-check-circle"></i>
                                        <span class="btn-text">Simpan Siswa</span>
                                        <span class="btn-loading d-none">
                                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            Menyimpan...
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
/* Form Styling */
.form-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.section-divider {
    margin: 0.75rem 0 1.5rem 0;
    border-color: #dee2e6;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.form-text {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Button Styling */
.btn {
    border-radius: 0.375rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #545b62;
    border-color: #545b62;
    transform: translateY(-1px);
}

/* Card Styling */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 0.75rem 0.75rem 0 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.125);
}

/* Alert Styling */
.alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

/* Loading State */
.btn-loading .btn-text {
    display: none;
}

.btn:not(.btn-loading) .btn-loading {
    display: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions .d-flex {
        flex-direction: column;
        align-items: stretch !important;
    }

    .form-actions .btn {
        margin-bottom: 0.5rem;
    }
}

/* Animation */
.form-section {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- Custom JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('formTambahSiswa');
    const submitBtn = document.getElementById('btnSubmit');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');

    // Form validation
    function validateForm() {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            const value = field.value.trim();

            if (!value) {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        });

        return isValid;
    }

    // Real-time validation
    form.addEventListener('input', function(e) {
        const field = e.target;
        if (field.hasAttribute('required')) {
            const value = field.value.trim();

            if (value) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            } else {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
            }
        }
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!validateForm()) {
            // Scroll to first invalid field
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                firstInvalid.focus();
            }
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.classList.add('btn-loading');
        btnText.style.display = 'none';
        btnLoading.classList.remove('d-none');

        // Submit form
        setTimeout(() => {
            form.submit();
        }, 500);
    });

    // Auto-dismiss alerts
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success')) {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }
        });
    }, 5000);

    // Enhanced select styling
    const selectElements = document.querySelectorAll('select.form-control');
    selectElements.forEach(select => {
        select.addEventListener('change', function() {
            if (this.value) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });

    // Form reset functionality
    const cancelBtn = document.querySelector('a[href*="/siswa"]');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function(e) {
            const hasChanges = Array.from(form.elements).some(element => {
                return element.value && element.value.trim() !== '';
            });

            if (hasChanges) {
                if (!confirm('Anda memiliki perubahan yang belum disimpan. Yakin ingin keluar?')) {
                    e.preventDefault();
                }
            }
        });
    }
});
</script>