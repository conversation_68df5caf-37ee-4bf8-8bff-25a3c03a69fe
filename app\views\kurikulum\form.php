<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-book-half text-primary"></i>
                        <?= isset($kurikulum) ? 'Edit Kurikulum' : 'Tambah Kurikulum Baru' ?>
                    </h1>
                    <p class="text-muted mb-0">
                        <?= isset($kurikulum) ? 'Perbarui informasi kurikulum' : 'Lengkapi form di bawah untuk menambahkan kurikulum baru' ?>
                    </p>
                </div>
                <div>
                    <a href="/siswa-app/public/kurikulum" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Error!</strong> <?= htmlspecialchars($_SESSION['error']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['error']); endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i>
        <strong>Berhasil!</strong> <?= htmlspecialchars($_SESSION['success']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['success']); endif; ?>

    <!-- Main Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-book-half"></i>
                        <?= isset($kurikulum) ? 'Form Edit Kurikulum' : 'Form Tambah Kurikulum' ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= isset($kurikulum) ? '/siswa-app/public/kurikulum/edit/' . $kurikulum['id_kurikulum'] : '/siswa-app/public/kurikulum/create' ?>" id="formKurikulum" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <!-- Basic Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="bi bi-info-circle text-primary"></i>
                                Informasi Dasar Kurikulum
                            </h6>
                            <hr class="section-divider">
                            
                            <div class="row">
                                <!-- Nama Kurikulum -->
                                <div class="col-md-12 mb-3">
                                    <label for="nama_kurikulum" class="form-label required">
                                        <i class="bi bi-book-half"></i>
                                        Nama Kurikulum
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="nama_kurikulum"
                                           name="nama_kurikulum"
                                           value="<?= htmlspecialchars($kurikulum['nama_kurikulum'] ?? '') ?>"
                                           placeholder="Contoh: Kurikulum Merdeka"
                                           required
                                           autocomplete="off">
                                    <div class="invalid-feedback">
                                        Nama kurikulum harus diisi
                                    </div>
                                    <small class="form-text text-muted">
                                        Masukkan nama kurikulum yang jelas dan mudah diidentifikasi
                                    </small>
                                </div>

                                <!-- Kode Kurikulum -->
                                <div class="col-md-6 mb-3">
                                    <label for="kode_kurikulum" class="form-label required">
                                        <i class="bi bi-tag"></i>
                                        Kode Kurikulum
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="kode_kurikulum"
                                           name="kode_kurikulum"
                                           value="<?= htmlspecialchars($kurikulum['kode_kurikulum'] ?? '') ?>"
                                           placeholder="Contoh: MERDEKA"
                                           required
                                           autocomplete="off"
                                           style="text-transform: uppercase;">
                                    <div class="invalid-feedback">
                                        Kode kurikulum harus diisi
                                    </div>
                                    <small class="form-text text-muted">
                                        Kode unik untuk kurikulum (huruf besar, angka, underscore)
                                    </small>
                                </div>

                                <!-- Status Aktif -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-toggle-on"></i>
                                        Status
                                    </label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active"
                                               <?= (isset($kurikulum) && $kurikulum['is_active']) || !isset($kurikulum) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active">
                                            <span class="status-text">
                                                <?= (isset($kurikulum) && $kurikulum['is_active']) || !isset($kurikulum) ? 'Aktif' : 'Non-aktif' ?>
                                            </span>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        Kurikulum aktif akan muncul dalam pilihan saat membuat kelas
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Description Section -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="bi bi-file-text text-success"></i>
                                Deskripsi Kurikulum
                            </h6>
                            <hr class="section-divider">
                            
                            <div class="row">
                                <!-- Deskripsi -->
                                <div class="col-md-12 mb-3">
                                    <label for="deskripsi" class="form-label">
                                        <i class="bi bi-file-text"></i>
                                        Deskripsi
                                    </label>
                                    <textarea class="form-control"
                                              id="deskripsi"
                                              name="deskripsi"
                                              rows="4"
                                              placeholder="Masukkan deskripsi kurikulum..."><?= htmlspecialchars($kurikulum['deskripsi'] ?? '') ?></textarea>
                                    <small class="form-text text-muted">
                                        Jelaskan karakteristik dan tujuan kurikulum ini (opsional)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <hr>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        Field dengan tanda <span class="text-danger">*</span> wajib diisi
                                    </small>
                                </div>
                                <div>
                                    <a href="/siswa-app/public/kurikulum" class="btn btn-secondary mr-2">
                                        <i class="bi bi-x-circle"></i>
                                        Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="btnSubmit">
                                        <i class="bi bi-check-circle"></i>
                                        <span class="btn-text">
                                            <?= isset($kurikulum) ? 'Update Kurikulum' : 'Simpan Kurikulum' ?>
                                        </span>
                                        <span class="btn-loading d-none">
                                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            Menyimpan...
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
/* Form Styling */
.form-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
    animation: fadeInUp 0.5s ease-out;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.section-divider {
    margin: 0.75rem 0 1.5rem 0;
    border-color: #dee2e6;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.form-text {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Switch Styling */
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.form-check-input:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Button Styling */
.btn {
    border-radius: 0.375rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-secondary:hover {
    background-color: #545b62;
    border-color: #545b62;
    transform: translateY(-1px);
}

/* Card Styling */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 0.75rem 0.75rem 0 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.125);
}

/* Loading State */
.btn-loading .btn-text {
    display: none;
}

.btn:not(.btn-loading) .btn-loading {
    display: none !important;
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions .d-flex {
        flex-direction: column;
        align-items: stretch !important;
    }

    .form-actions .btn {
        margin-bottom: 0.5rem;
    }
}
</style>

<!-- Custom JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('formKurikulum');
    const submitBtn = document.getElementById('btnSubmit');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    const statusSwitch = document.getElementById('is_active');
    const statusText = document.querySelector('.status-text');
    const kodeInput = document.getElementById('kode_kurikulum');

    // Form validation
    function validateForm() {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            const value = field.value.trim();

            if (!value) {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        });

        // Validate kode format
        const kodeValue = kodeInput.value.trim();
        if (kodeValue && !/^[A-Z0-9_]+$/.test(kodeValue)) {
            kodeInput.classList.add('is-invalid');
            kodeInput.classList.remove('is-valid');
            isValid = false;
        }

        return isValid;
    }

    // Real-time validation
    form.addEventListener('input', function(e) {
        const field = e.target;
        if (field.hasAttribute('required')) {
            const value = field.value.trim();

            if (value) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            } else {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
            }
        }

        // Special validation for kode
        if (field.id === 'kode_kurikulum') {
            const value = field.value.trim();
            if (value && !/^[A-Z0-9_]+$/.test(value)) {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
            } else if (value) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        }
    });

    // Auto-uppercase kode kurikulum
    kodeInput.addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9_]/g, '');
    });

    // Status switch handler
    statusSwitch.addEventListener('change', function() {
        statusText.textContent = this.checked ? 'Aktif' : 'Non-aktif';
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!validateForm()) {
            // Scroll to first invalid field
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                firstInvalid.focus();
            }
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.classList.add('btn-loading');
        btnText.style.display = 'none';
        btnLoading.classList.remove('d-none');

        // Submit form
        setTimeout(() => {
            form.submit();
        }, 500);
    });

    // Auto-dismiss alerts
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success')) {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }
        });
    }, 5000);

    // Confirmation before leaving with unsaved changes
    const cancelBtn = document.querySelector('a[href*="/kurikulum"]');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function(e) {
            const hasChanges = Array.from(form.elements).some(element => {
                if (element.type === 'checkbox') {
                    return element.checked !== element.defaultChecked;
                }
                return element.value && element.value.trim() !== '';
            });

            if (hasChanges) {
                if (!confirm('Anda memiliki perubahan yang belum disimpan. Yakin ingin keluar?')) {
                    e.preventDefault();
                }
            }
        });
    }
});
</script>
