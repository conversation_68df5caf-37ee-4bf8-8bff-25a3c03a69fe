<?php
/**
 * Script untuk memperbaiki sistem wali kelas
 * Menambahkan struktur kelas: KPP A, KPP B, KPP C, X-1, X-2, XI-1, XI-2, XII-1, XII-2, KPA
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🏫 Perbaikan Sistem Wali Kelas</h1>\n";
echo "<pre>\n";

// Include database config
require_once __DIR__ . '/app/config/db.php';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Koneksi database berhasil\n";
    
    // Read and execute fix script
    $sqlFile = __DIR__ . '/database/fix_wali_kelas_system.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("File SQL tidak ditemukan: " . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "📄 Menjalankan " . count($statements) . " statement SQL...\n\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        try {
            if (trim($statement)) {
                $pdo->exec($statement);
                
                // Show progress for important statements
                if (stripos($statement, 'ALTER TABLE users') !== false) {
                    echo "✅ Role enum berhasil diupdate\n";
                } elseif (stripos($statement, 'ALTER TABLE kelas') !== false) {
                    echo "✅ Struktur tabel kelas berhasil diupdate\n";
                } elseif (stripos($statement, 'ALTER TABLE catatan_siswa') !== false) {
                    echo "✅ Enum jenis_catatan berhasil diupdate\n";
                } elseif (stripos($statement, 'INSERT') !== false && stripos($statement, 'kategori_catatan') !== false) {
                    echo "✅ Kategori catatan wali kelas berhasil ditambahkan\n";
                } elseif (stripos($statement, 'INSERT') !== false && stripos($statement, 'kelas') !== false) {
                    echo "✅ Data kelas baru berhasil ditambahkan\n";
                } elseif (stripos($statement, 'INSERT') !== false && stripos($statement, 'users') !== false) {
                    echo "✅ User wali kelas sample berhasil ditambahkan\n";
                } elseif (stripos($statement, 'CREATE TABLE') !== false && stripos($statement, 'user_kelas_mapping') !== false) {
                    echo "✅ Tabel user_kelas_mapping berhasil dibuat\n";
                }
                
                $successCount++;
            }
        } catch (PDOException $e) {
            // Skip errors for statements that might already exist
            if (stripos($e->getMessage(), 'Duplicate entry') !== false ||
                stripos($e->getMessage(), 'already exists') !== false ||
                stripos($e->getMessage(), 'Multiple primary key') !== false) {
                echo "⚠️  Skipped (already exists): " . substr($statement, 0, 50) . "...\n";
                $successCount++;
            } else {
                echo "❌ Error pada statement " . ($index + 1) . ": " . $e->getMessage() . "\n";
                $errorCount++;
            }
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "📊 RINGKASAN PERBAIKAN WALI KELAS:\n";
    echo "✅ Berhasil: $successCount statement\n";
    echo "❌ Error: $errorCount statement\n";
    
    // Verify wali kelas system
    echo "\n🔍 VERIFIKASI SISTEM WALI KELAS:\n";
    
    // Check updated kelas
    echo "\n📚 DAFTAR KELAS:\n";
    $result = $pdo->query("
        SELECT id, nama_kelas, tingkat, wali_kelas, wali_kelas_id 
        FROM kelas 
        WHERE is_active = 1 
        ORDER BY 
            CASE tingkat 
                WHEN 'KPP' THEN 1 
                WHEN 'X' THEN 2 
                WHEN 'XI' THEN 3 
                WHEN 'XII' THEN 4 
                WHEN 'KPA' THEN 5 
                ELSE 6 
            END, nama_kelas
    ");
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $waliStatus = $row['wali_kelas_id'] ? "✅ Assigned" : "⚠️  Not assigned";
        echo "  {$row['id']}. {$row['nama_kelas']} (Tingkat: {$row['tingkat']}) - $waliStatus\n";
    }
    
    // Check wali kelas users
    echo "\n👥 USER WALI KELAS:\n";
    $result = $pdo->query("
        SELECT id, username, nama_lengkap, role 
        FROM users 
        WHERE role = 'wali_kelas' 
        ORDER BY username
    ");
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "  {$row['id']}. {$row['username']} - {$row['nama_lengkap']}\n";
    }
    
    // Check kategori catatan wali kelas
    echo "\n📝 KATEGORI CATATAN WALI KELAS:\n";
    $result = $pdo->query("
        SELECT kode_kategori, nama_kategori 
        FROM kategori_catatan 
        WHERE kode_kategori LIKE 'wali_%' 
        ORDER BY kode_kategori
    ");
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "  ✅ {$row['kode_kategori']} - {$row['nama_kategori']}\n";
    }
    
    // Test assignment functionality
    echo "\n🔧 ASSIGNMENT WALI KELAS KE KELAS:\n";
    
    // Auto-assign wali kelas to their respective classes
    $assignments = [
        'wali_kpp_a' => 'KPP A',
        'wali_kpp_b' => 'KPP B', 
        'wali_kpp_c' => 'KPP C',
        'wali_x_1' => 'X-1',
        'wali_x_2' => 'X-2',
        'wali_xi_1' => 'XI-1',
        'wali_xi_2' => 'XI-2',
        'wali_xii_1' => 'XII-1',
        'wali_xii_2' => 'XII-2',
        'wali_kpa' => 'KPA'
    ];
    
    foreach ($assignments as $username => $namaKelas) {
        try {
            $pdo->exec("
                UPDATE kelas k
                JOIN users u ON u.username = '$username'
                SET k.wali_kelas_id = u.id, k.wali_kelas = u.nama_lengkap
                WHERE k.nama_kelas = '$namaKelas' AND k.is_active = 1
            ");
            echo "  ✅ $username assigned to $namaKelas\n";
        } catch (Exception $e) {
            echo "  ❌ Failed to assign $username to $namaKelas: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "🎉 PERBAIKAN SISTEM WALI KELAS SELESAI!\n\n";
    
    echo "📋 FITUR YANG TELAH DITAMBAHKAN:\n";
    echo "✅ Struktur kelas lengkap: KPP A/B/C, X-1/2, XI-1/2, XII-1/2, KPA\n";
    echo "✅ User wali kelas untuk setiap kelas\n";
    echo "✅ Kategori catatan spesifik per wali kelas\n";
    echo "✅ Role-based access control untuk catatan\n";
    echo "✅ Auto-assignment wali kelas ke kelas masing-masing\n";
    
    echo "\n📋 CARA MENGGUNAKAN:\n";
    echo "1. Login sebagai admin: admin / password\n";
    echo "2. Atau login sebagai wali kelas:\n";
    echo "   - wali_kpp_a / password (untuk KPP A)\n";
    echo "   - wali_x_1 / password (untuk X-1)\n";
    echo "   - dst...\n";
    echo "3. Wali kelas hanya bisa:\n";
    echo "   - Melihat siswa di kelas mereka\n";
    echo "   - Membuat catatan untuk siswa di kelas mereka\n";
    echo "   - Menggunakan kategori catatan sesuai kelas mereka\n";
    
    echo "\n📋 TESTING:\n";
    echo "1. Login sebagai wali_kpp_a\n";
    echo "2. Buka menu Siswa - hanya siswa KPP A yang tampil\n";
    echo "3. Buat catatan siswa - hanya kategori 'Wali Kelas KPP A' yang tersedia\n";
    echo "4. Coba akses siswa dari kelas lain - akan ditolak\n";
    
} catch (Exception $e) {
    echo "❌ FATAL ERROR: " . $e->getMessage() . "\n";
    echo "\nPastikan:\n";
    echo "1. MySQL server berjalan\n";
    echo "2. Database siswa_app sudah ada\n";
    echo "3. Kredensial database di app/config/db.php benar\n";
}

echo "</pre>\n";

// Add some styling
echo "<style>
body { font-family: 'Courier New', monospace; background: #f5f5f5; margin: 20px; }
h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
pre { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
</style>";
?>
