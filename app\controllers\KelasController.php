<?php
require_once __DIR__ . '/../models/Kelas.php';
require_once __DIR__ . '/../helpers/Security.php';

class KelasController {
    private $kelasModel;

    public function __construct() {
        $this->kelasModel = new Kelas();
    }

    public function index() {
        Security::requireAuth();

        // Get user role and info
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        $userId = $_SESSION['user_id'] ?? 0;
        $userName = $_SESSION['username'] ?? '';

        // Get kelas based on user role
        $kelas = $this->getKelasBasedOnRole($userRole, $userId, $userName);

        // Check if user can manage kelas (add/edit/delete)
        $canManage = $this->canManageKelas($userRole);

        $data = [
            'title' => 'Daftar Kelas',
            'kelas' => $kelas,
            'csrf_token' => Security::generateCSRFToken(),
            'user_role' => $userRole,
            'can_manage' => $canManage
        ];
        $this->view('kelas/list', $data);
    }

    /**
     * Get kelas based on user role
     */
    private function getKelasBasedOnRole($role, $userId, $userName) {
        switch($role) {
            case 'admin':
                // Admin can see all kelas
                return $this->kelasModel->getAll();

            case 'pamong':
                // Pamong can only see kelas based on their level
                return $this->getKelasByPamongLevel($userName);

            case 'wali_kelas':
                // Wali kelas can only see their own kelas
                return $this->getKelasByWaliKelas($userName);

            case 'staff':
                // Staff can see all kelas (read-only)
                return $this->kelasModel->getAll();

            default:
                return [];
        }
    }

    /**
     * Get kelas by pamong level
     */
    private function getKelasByPamongLevel($userName) {
        // Determine pamong level from username
        $pamongLevel = '';
        if (strpos($userName, 'MP') !== false) {
            $pamongLevel = 'KPP'; // MP handles KPP
        } elseif (strpos($userName, 'MT') !== false) {
            $pamongLevel = 'X'; // MT handles X
        } elseif (strpos($userName, 'MM') !== false) {
            $pamongLevel = 'XI'; // MM handles XI
        } elseif (strpos($userName, 'MU') !== false) {
            $pamongLevel = 'XII,KPA'; // MU handles XII and KPA
        }

        if ($pamongLevel) {
            return $this->kelasModel->getByTingkat($pamongLevel);
        }

        return [];
    }

    /**
     * Get kelas by wali kelas name
     */
    private function getKelasByWaliKelas($userName) {
        return $this->kelasModel->getByWaliKelas($userName);
    }

    /**
     * Check if user can manage kelas (add/edit/delete)
     */
    private function canManageKelas($role) {
        return $role === 'admin';
    }

    public function create() {
        Security::requireAuth();

        // Check if user can manage kelas
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        if (!$this->canManageKelas($userRole)) {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk menambah kelas.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas/create');
                exit;
            }

            $nama_kelas = Security::sanitizeInput($_POST['nama_kelas'] ?? '');
            $tingkat = Security::sanitizeInput($_POST['tingkat'] ?? '');
            $kurikulum_id = Security::sanitizeInput($_POST['kurikulum_id'] ?? '');
            $tahun_pelajaran = Security::sanitizeInput($_POST['tahun_pelajaran'] ?? '');
            $wali_kelas = Security::sanitizeInput($_POST['wali_kelas'] ?? '');

            if ($nama_kelas && $tingkat && $tahun_pelajaran) {
                $data = [
                    'nama_kelas' => $nama_kelas,
                    'tingkat' => $tingkat,
                    'kurikulum_id' => $kurikulum_id ?: null,
                    'tahun_pelajaran' => $tahun_pelajaran,
                    'wali_kelas' => $wali_kelas,
                    'created_by' => $_SESSION['user_id'] ?? 1
                ];

                $result = $this->kelasModel->createKelas($data);
                if ($result) {
                    $_SESSION['success'] = 'Kelas berhasil ditambahkan.';
                    header('Location: /siswa-app/public/kelas');
                    exit;
                } else {
                    $_SESSION['error'] = 'Gagal menambahkan kelas.';
                }
            } else {
                $_SESSION['error'] = 'Nama kelas, tingkat, dan tahun pelajaran wajib diisi.';
            }
        }

        $data = [
            'title' => 'Tambah Kelas',
            'kurikulum' => $this->kelasModel->getActiveKurikulum(),
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null
        ];

        // Clear messages
        unset($_SESSION['success'], $_SESSION['error']);

        $this->view('kelas/form', $data);
    }

    public function edit($id) {
        Security::requireAuth();

        // Check if user can manage kelas
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        if (!$this->canManageKelas($userRole)) {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk mengedit kelas.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        $kelas = $this->kelasModel->getById($id);
        if (!$kelas) {
            $_SESSION['error'] = 'Kelas tidak ditemukan.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas/edit/' . $id);
                exit;
            }

            $nama_kelas = Security::sanitizeInput($_POST['nama_kelas'] ?? '');
            $tingkat = Security::sanitizeInput($_POST['tingkat'] ?? '');
            $kurikulum_id = Security::sanitizeInput($_POST['kurikulum_id'] ?? '');
            $tahun_pelajaran = Security::sanitizeInput($_POST['tahun_pelajaran'] ?? '');
            $wali_kelas = Security::sanitizeInput($_POST['wali_kelas'] ?? '');

            if ($nama_kelas && $tingkat && $tahun_pelajaran) {
                $data = [
                    'nama_kelas' => $nama_kelas,
                    'tingkat' => $tingkat,
                    'kurikulum_id' => $kurikulum_id ?: null,
                    'tahun_pelajaran' => $tahun_pelajaran,
                    'wali_kelas' => $wali_kelas,
                    'updated_by' => $_SESSION['user_id'] ?? 1
                ];

                $result = $this->kelasModel->updateKelas($id, $data);
                if ($result) {
                    $_SESSION['success'] = 'Kelas berhasil diperbarui.';
                    header('Location: /siswa-app/public/kelas');
                    exit;
                } else {
                    $_SESSION['error'] = 'Gagal memperbarui kelas.';
                }
            } else {
                $_SESSION['error'] = 'Nama kelas, tingkat, dan tahun pelajaran wajib diisi.';
            }
        }

        $data = [
            'title' => 'Edit Kelas',
            'kelas' => $kelas,
            'kurikulum' => $this->kelasModel->getActiveKurikulum(),
            'csrf_token' => Security::generateCSRFToken(),
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null
        ];

        // Clear messages
        unset($_SESSION['success'], $_SESSION['error']);

        $this->view('kelas/form', $data);
    }

    public function detail($id) {
        Security::requireAuth();

        // Get user info for access control
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        $userName = $_SESSION['username'] ?? '';

        $kelas = $this->kelasModel->getById($id);
        if (!$kelas) {
            $_SESSION['error'] = 'Kelas tidak ditemukan.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        // Check if user has access to this specific kelas
        if (!$this->canAccessKelas($userRole, $userName, $kelas)) {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk melihat kelas ini.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        // Get students in this class
        require_once __DIR__ . '/../models/Siswa.php';
        $siswaModel = new Siswa();
        $siswaList = $siswaModel->getByKelasId($id);

        $data = [
            'title' => 'Detail Kelas - ' . $kelas['nama_kelas'],
            'kelas' => $kelas,
            'siswa_list' => $siswaList,
            'csrf_token' => Security::generateCSRFToken()
        ];

        $this->view('kelas/detail', $data);
    }

    public function delete($id) {
        Security::requireAuth();

        // Check if user can manage kelas
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        if (!$this->canManageKelas($userRole)) {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk menghapus kelas.';
            header('Location: /siswa-app/public/kelas');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kelas');
                exit;
            }

            try {
                $result = $this->kelasModel->deleteKelas($id);
                if ($result) {
                    $_SESSION['success'] = 'Kelas berhasil dihapus.';
                } else {
                    $_SESSION['error'] = 'Gagal menghapus kelas.';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'Error: ' . $e->getMessage();
            }
        } else {
            // For GET request, show delete confirmation page
            $kelas = $this->kelasModel->getById($id);
            if (!$kelas) {
                $_SESSION['error'] = 'Kelas tidak ditemukan.';
                header('Location: /siswa-app/public/kelas');
                exit;
            }

            // Show confirmation page
            $data = [
                'title' => 'Hapus Kelas',
                'kelas' => $kelas,
                'csrf_token' => Security::generateCSRFToken()
            ];

            $this->view('kelas/delete_confirm', $data);
            return;
        }

        header('Location: /siswa-app/public/kelas');
        exit;
    }

    /**
     * Check if user can access specific kelas
     */
    private function canAccessKelas($role, $userName, $kelas) {
        switch($role) {
            case 'admin':
            case 'staff':
                // Admin and staff can access all kelas
                return true;

            case 'pamong':
                // Pamong can only access kelas based on their level
                $allowedTingkat = [];
                if (strpos($userName, 'MP') !== false) {
                    $allowedTingkat = ['KPP'];
                } elseif (strpos($userName, 'MT') !== false) {
                    $allowedTingkat = ['X'];
                } elseif (strpos($userName, 'MM') !== false) {
                    $allowedTingkat = ['XI'];
                } elseif (strpos($userName, 'MU') !== false) {
                    $allowedTingkat = ['XII', 'KPA'];
                }

                return in_array($kelas['tingkat'], $allowedTingkat);

            case 'wali_kelas':
                // Wali kelas can only access their own kelas
                return $kelas['wali_kelas'] === $userName;

            default:
                return false;
        }
    }

    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
?>