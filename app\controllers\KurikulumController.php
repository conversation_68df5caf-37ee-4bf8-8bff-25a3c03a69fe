<?php

require_once 'app/models/Kurikulum.php';
require_once 'app/helpers/Security.php';

class KurikulumController {
    private $kurikulumModel;

    public function __construct() {
        $this->kurikulumModel = new Kurikulum();
    }

    /**
     * Display list of kurikulum
     */
    public function index() {
        Security::requireAuth();
        
        // Only admin can manage kurikulum
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        if ($userRole !== 'admin') {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk mengelola kurikulum.';
            header('Location: /siswa-app/public/dashboard');
            exit;
        }
        
        $data = [
            'title' => 'Manajemen Kurikulum',
            'kurikulum' => $this->kurikulumModel->getUsageStats(),
            'csrf_token' => Security::generateCSRFToken()
        ];
        
        $this->view('kurikulum/list', $data);
    }

    /**
     * Show create form
     */
    public function create() {
        Security::requireAuth();
        
        // Only admin can manage kurikulum
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        if ($userRole !== 'admin') {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk menambah kurikulum.';
            header('Location: /siswa-app/public/kurikulum');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kurikulum/create');
                exit;
            }

            try {
                // Validate input
                $data = [
                    'nama_kurikulum' => trim($_POST['nama_kurikulum'] ?? ''),
                    'kode_kurikulum' => strtoupper(trim($_POST['kode_kurikulum'] ?? '')),
                    'deskripsi' => trim($_POST['deskripsi'] ?? ''),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];

                // Validate data
                $errors = $this->kurikulumModel->validate($data);
                if (!empty($errors)) {
                    $_SESSION['error'] = implode(', ', $errors);
                    header('Location: /siswa-app/public/kurikulum/create');
                    exit;
                }

                // Create kurikulum
                $result = $this->kurikulumModel->create($data);
                if ($result) {
                    $_SESSION['success'] = 'Kurikulum berhasil ditambahkan.';
                    header('Location: /siswa-app/public/kurikulum');
                    exit;
                } else {
                    $_SESSION['error'] = 'Gagal menambahkan kurikulum.';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = $e->getMessage();
            }

            header('Location: /siswa-app/public/kurikulum/create');
            exit;
        }

        $data = [
            'title' => 'Tambah Kurikulum',
            'csrf_token' => Security::generateCSRFToken()
        ];
        
        $this->view('kurikulum/form', $data);
    }

    /**
     * Show edit form
     */
    public function edit($id) {
        Security::requireAuth();
        
        // Only admin can manage kurikulum
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        if ($userRole !== 'admin') {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk mengedit kurikulum.';
            header('Location: /siswa-app/public/kurikulum');
            exit;
        }

        $kurikulum = $this->kurikulumModel->getById($id);
        if (!$kurikulum) {
            $_SESSION['error'] = 'Kurikulum tidak ditemukan.';
            header('Location: /siswa-app/public/kurikulum');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Verify CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!Security::verifyCSRFToken($csrfToken)) {
                $_SESSION['error'] = 'Token keamanan tidak valid.';
                header('Location: /siswa-app/public/kurikulum/edit/' . $id);
                exit;
            }

            try {
                // Validate input
                $data = [
                    'nama_kurikulum' => trim($_POST['nama_kurikulum'] ?? ''),
                    'kode_kurikulum' => strtoupper(trim($_POST['kode_kurikulum'] ?? '')),
                    'deskripsi' => trim($_POST['deskripsi'] ?? ''),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];

                // Validate data
                $errors = $this->kurikulumModel->validate($data, $id);
                if (!empty($errors)) {
                    $_SESSION['error'] = implode(', ', $errors);
                    header('Location: /siswa-app/public/kurikulum/edit/' . $id);
                    exit;
                }

                // Update kurikulum
                $result = $this->kurikulumModel->update($id, $data);
                if ($result) {
                    $_SESSION['success'] = 'Kurikulum berhasil diperbarui.';
                    header('Location: /siswa-app/public/kurikulum');
                    exit;
                } else {
                    $_SESSION['error'] = 'Gagal memperbarui kurikulum.';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = $e->getMessage();
            }

            header('Location: /siswa-app/public/kurikulum/edit/' . $id);
            exit;
        }

        $data = [
            'title' => 'Edit Kurikulum',
            'kurikulum' => $kurikulum,
            'csrf_token' => Security::generateCSRFToken()
        ];
        
        $this->view('kurikulum/form', $data);
    }

    /**
     * Delete kurikulum
     */
    public function delete($id) {
        Security::requireAuth();
        
        // Only admin can manage kurikulum
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        if ($userRole !== 'admin') {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk menghapus kurikulum.';
            header('Location: /siswa-app/public/kurikulum');
            exit;
        }

        try {
            $result = $this->kurikulumModel->delete($id);
            if ($result) {
                $_SESSION['success'] = 'Kurikulum berhasil dihapus.';
            } else {
                $_SESSION['error'] = 'Gagal menghapus kurikulum.';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }

        header('Location: /siswa-app/public/kurikulum');
        exit;
    }

    /**
     * Toggle active status
     */
    public function toggle($id) {
        Security::requireAuth();
        
        // Only admin can manage kurikulum
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        if ($userRole !== 'admin') {
            $_SESSION['error'] = 'Anda tidak memiliki akses untuk mengubah status kurikulum.';
            header('Location: /siswa-app/public/kurikulum');
            exit;
        }

        try {
            $result = $this->kurikulumModel->toggleActive($id);
            if ($result) {
                $_SESSION['success'] = 'Status kurikulum berhasil diubah.';
            } else {
                $_SESSION['error'] = 'Gagal mengubah status kurikulum.';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }

        header('Location: /siswa-app/public/kurikulum');
        exit;
    }

    /**
     * Load view
     */
    private function view($view, $data = []) {
        extract($data);
        require_once "app/views/$view.php";
    }
}
