<?php

class Kurikulum {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * Get all kurikulum
     */
    public function getAll() {
        try {
            return $this->db->fetchAll("
                SELECT id_kurikulum, nama_kurikulum, kode_kurikulum, deskripsi, is_active, created_at, updated_at
                FROM kurikulum
                ORDER BY nama_kurikulum ASC
            ");
        } catch (Exception $e) {
            error_log("Error in Kurikulum::getAll: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get active kurikulum only
     */
    public function getActive() {
        try {
            return $this->db->fetchAll("
                SELECT id_kurikulum, nama_kurikulum, kode_kurikulum, deskripsi
                FROM kurikulum
                WHERE is_active = 1
                ORDER BY nama_kurikulum ASC
            ");
        } catch (Exception $e) {
            error_log("Error in Kurikulum::getActive: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get kurikulum by ID
     */
    public function getById($id) {
        try {
            return $this->db->fetchOne("
                SELECT id_kurikulum, nama_kurikulum, kode_kurikulum, deskripsi, is_active, created_at, updated_at
                FROM kurikulum
                WHERE id_kurikulum = ?
            ", [$id]);
        } catch (Exception $e) {
            error_log("Error in Kurikulum::getById: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create new kurikulum
     */
    public function create($data) {
        try {
            // Validate required fields
            if (empty($data['nama_kurikulum']) || empty($data['kode_kurikulum'])) {
                throw new Exception('Nama kurikulum dan kode kurikulum harus diisi');
            }

            // Check if kode_kurikulum already exists
            $existing = $this->db->fetchOne("
                SELECT id_kurikulum FROM kurikulum WHERE kode_kurikulum = ?
            ", [$data['kode_kurikulum']]);

            if ($existing) {
                throw new Exception('Kode kurikulum sudah digunakan');
            }

            $result = $this->db->execute("
                INSERT INTO kurikulum (nama_kurikulum, kode_kurikulum, deskripsi, is_active, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ", [
                $data['nama_kurikulum'],
                $data['kode_kurikulum'],
                $data['deskripsi'] ?? '',
                $data['is_active'] ?? 1
            ]);

            return $result;
        } catch (Exception $e) {
            error_log("Error in Kurikulum::create: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update kurikulum
     */
    public function update($id, $data) {
        try {
            // Validate required fields
            if (empty($data['nama_kurikulum']) || empty($data['kode_kurikulum'])) {
                throw new Exception('Nama kurikulum dan kode kurikulum harus diisi');
            }

            // Check if kode_kurikulum already exists (excluding current record)
            $existing = $this->db->fetchOne("
                SELECT id_kurikulum FROM kurikulum 
                WHERE kode_kurikulum = ? AND id_kurikulum != ?
            ", [$data['kode_kurikulum'], $id]);

            if ($existing) {
                throw new Exception('Kode kurikulum sudah digunakan');
            }

            $result = $this->db->execute("
                UPDATE kurikulum 
                SET nama_kurikulum = ?, kode_kurikulum = ?, deskripsi = ?, is_active = ?, updated_at = NOW()
                WHERE id_kurikulum = ?
            ", [
                $data['nama_kurikulum'],
                $data['kode_kurikulum'],
                $data['deskripsi'] ?? '',
                $data['is_active'] ?? 1,
                $id
            ]);

            return $result;
        } catch (Exception $e) {
            error_log("Error in Kurikulum::update: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete kurikulum
     */
    public function delete($id) {
        try {
            // Check if kurikulum is being used by any kelas
            $usage = $this->db->fetchOne("
                SELECT COUNT(*) as count FROM kelas WHERE kurikulum_id = ?
            ", [$id]);

            if ($usage && $usage['count'] > 0) {
                throw new Exception('Kurikulum tidak dapat dihapus karena masih digunakan oleh ' . $usage['count'] . ' kelas');
            }

            $result = $this->db->execute("
                DELETE FROM kurikulum WHERE id_kurikulum = ?
            ", [$id]);

            return $result;
        } catch (Exception $e) {
            error_log("Error in Kurikulum::delete: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Toggle active status
     */
    public function toggleActive($id) {
        try {
            $result = $this->db->execute("
                UPDATE kurikulum 
                SET is_active = NOT is_active, updated_at = NOW()
                WHERE id_kurikulum = ?
            ", [$id]);

            return $result;
        } catch (Exception $e) {
            error_log("Error in Kurikulum::toggleActive: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get kurikulum usage statistics
     */
    public function getUsageStats() {
        try {
            return $this->db->fetchAll("
                SELECT 
                    k.id_kurikulum,
                    k.nama_kurikulum,
                    k.kode_kurikulum,
                    COUNT(kl.id_kelas) as jumlah_kelas,
                    k.is_active
                FROM kurikulum k
                LEFT JOIN kelas kl ON k.id_kurikulum = kl.kurikulum_id
                GROUP BY k.id_kurikulum, k.nama_kurikulum, k.kode_kurikulum, k.is_active
                ORDER BY k.nama_kurikulum ASC
            ");
        } catch (Exception $e) {
            error_log("Error in Kurikulum::getUsageStats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Validate kurikulum data
     */
    public function validate($data, $id = null) {
        $errors = [];

        // Required fields
        if (empty($data['nama_kurikulum'])) {
            $errors[] = 'Nama kurikulum harus diisi';
        }

        if (empty($data['kode_kurikulum'])) {
            $errors[] = 'Kode kurikulum harus diisi';
        }

        // Validate kode format (uppercase, no spaces)
        if (!empty($data['kode_kurikulum'])) {
            if (!preg_match('/^[A-Z0-9_]+$/', $data['kode_kurikulum'])) {
                $errors[] = 'Kode kurikulum hanya boleh menggunakan huruf besar, angka, dan underscore';
            }
        }

        // Check uniqueness
        if (!empty($data['kode_kurikulum'])) {
            $whereClause = $id ? "AND id_kurikulum != ?" : "";
            $params = $id ? [$data['kode_kurikulum'], $id] : [$data['kode_kurikulum']];
            
            $existing = $this->db->fetchOne("
                SELECT id_kurikulum FROM kurikulum 
                WHERE kode_kurikulum = ? $whereClause
            ", $params);

            if ($existing) {
                $errors[] = 'Kode kurikulum sudah digunakan';
            }
        }

        return $errors;
    }
}
