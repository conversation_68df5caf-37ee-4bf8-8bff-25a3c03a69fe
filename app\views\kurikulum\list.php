<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-book-half text-primary"></i>
                        Manajemen Kurikulum
                    </h1>
                    <p class="text-muted mb-0"><PERSON><PERSON><PERSON> jenis kurikulum yang digunakan dalam sistem akademik</p>
                </div>
                <div>
                    <a href="/siswa-app/public/kurikulum/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        Tambah Kurikulum Baru
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Error!</strong> <?= htmlspecialchars($_SESSION['error']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['error']); endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i>
        <strong>Berhasil!</strong> <?= htmlspecialchars($_SESSION['success']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['success']); endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Kurikulum
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= count($kurikulum) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-book-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Kurikulum Aktif
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= count(array_filter($kurikulum, function($k) { return $k['is_active'] == 1; })) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Kelas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= array_sum(array_column($kurikulum, 'jumlah_kelas')) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Tahun Akademik
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                2024/2025
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-list"></i>
                        Daftar Kurikulum
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($kurikulum)): ?>
                        <div class="empty-state text-center py-5">
                            <i class="bi bi-book-half display-1 text-muted mb-3"></i>
                            <h4 class="text-muted">Belum Ada Kurikulum</h4>
                            <p class="text-muted mb-4">Mulai dengan menambahkan kurikulum pertama untuk sistem akademik Anda</p>
                            <a href="/siswa-app/public/kurikulum/create" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i>
                                Tambah Kurikulum Pertama
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="kurikulumTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="8%">
                                            <i class="bi bi-hash"></i>
                                            ID
                                        </th>
                                        <th width="25%">
                                            <i class="bi bi-book-half"></i>
                                            Nama Kurikulum
                                        </th>
                                        <th width="15%">
                                            <i class="bi bi-tag"></i>
                                            Kode
                                        </th>
                                        <th width="30%">
                                            <i class="bi bi-file-text"></i>
                                            Deskripsi
                                        </th>
                                        <th width="10%">
                                            <i class="bi bi-building"></i>
                                            Kelas
                                        </th>
                                        <th width="10%">
                                            <i class="bi bi-toggle-on"></i>
                                            Status
                                        </th>
                                        <th width="12%" class="text-center">
                                            <i class="bi bi-gear"></i>
                                            Aksi
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($kurikulum as $k): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                <?= htmlspecialchars($k['id_kurikulum']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="kurikulum-icon mr-2">
                                                    <i class="bi bi-book-half text-primary"></i>
                                                </div>
                                                <div>
                                                    <strong class="kurikulum-name">
                                                        <?= htmlspecialchars($k['nama_kurikulum']) ?>
                                                    </strong>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary text-white">
                                                <?= htmlspecialchars($k['kode_kurikulum']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?= htmlspecialchars(substr($k['deskripsi'] ?? '-', 0, 100)) ?>
                                                <?= strlen($k['deskripsi'] ?? '') > 100 ? '...' : '' ?>
                                            </small>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info text-white">
                                                <?= $k['jumlah_kelas'] ?> kelas
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <?php if ($k['is_active']): ?>
                                                <span class="badge bg-success text-white">
                                                    <i class="bi bi-check-circle"></i>
                                                    Aktif
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary text-white">
                                                    <i class="bi bi-x-circle"></i>
                                                    Non-aktif
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="/siswa-app/public/kurikulum/edit/<?= $k['id_kurikulum'] ?>"
                                                   class="btn btn-outline-warning"
                                                   title="Edit Kurikulum"
                                                   data-toggle="tooltip">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <a href="/siswa-app/public/kurikulum/toggle/<?= $k['id_kurikulum'] ?>"
                                                   class="btn btn-outline-<?= $k['is_active'] ? 'secondary' : 'success' ?>"
                                                   title="<?= $k['is_active'] ? 'Non-aktifkan' : 'Aktifkan' ?>"
                                                   data-toggle="tooltip"
                                                   onclick="return confirm('Yakin ingin mengubah status kurikulum ini?')">
                                                    <i class="bi bi-toggle-<?= $k['is_active'] ? 'off' : 'on' ?>"></i>
                                                </a>
                                                <button type="button"
                                                        class="btn btn-outline-danger"
                                                        onclick="confirmDelete(<?= $k['id_kurikulum'] ?>, '<?= htmlspecialchars($k['nama_kurikulum']) ?>', <?= $k['jumlah_kelas'] ?>)"
                                                        title="Hapus Kurikulum"
                                                        data-toggle="tooltip">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
/* Statistics Cards */
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-gray-800 { color: #5a5c69 !important; }
.text-gray-300 { color: #dddfeb !important; }
.font-weight-bold { font-weight: 700 !important; }
.text-xs { font-size: 0.7rem; }
.fa-2x { font-size: 2em; }

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.kurikulum-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
}

.kurikulum-name {
    font-size: 1rem;
    color: #2c3e50;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Button Enhancements */
.btn-group-sm .btn {
    padding: 0.375rem 0.5rem;
    margin: 0 1px;
    border-radius: 0.25rem;
    transition: all 0.15s ease-in-out;
}

.btn-outline-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.btn-outline-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 0.75rem 0.75rem 0 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.125);
}

/* Empty State */
.empty-state {
    padding: 3rem 1rem;
}

.empty-state .display-1 {
    font-size: 4rem;
    opacity: 0.3;
}
</style>

<!-- Custom JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    if (typeof $('[data-toggle="tooltip"]').tooltip === 'function') {
        $('[data-toggle="tooltip"]').tooltip();
    }

    // Auto-dismiss success alerts
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert-success');
        alerts.forEach(alert => {
            alert.style.transition = 'opacity 0.5s ease-out';
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 500);
        });
    }, 5000);
});

// Enhanced delete confirmation
function confirmDelete(id, namaKurikulum, jumlahKelas) {
    let warningMessage = '';
    if (jumlahKelas > 0) {
        warningMessage = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>Peringatan:</strong> Kurikulum ini digunakan oleh ${jumlahKelas} kelas.
                Hapus atau ubah kurikulum kelas tersebut terlebih dahulu.
            </div>
        `;
    }

    const modal = `
        <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="bi bi-exclamation-triangle"></i>
                            Konfirmasi Hapus Kurikulum
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            <i class="bi bi-book-half display-1 text-danger"></i>
                        </div>
                        <p class="text-center">
                            Apakah Anda yakin ingin menghapus kurikulum <strong>"${namaKurikulum}"</strong>?
                        </p>
                        ${warningMessage}
                        ${jumlahKelas === 0 ? `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Peringatan:</strong> Tindakan ini tidak dapat dibatalkan.
                        </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                            <i class="bi bi-x-circle"></i> Batal
                        </button>
                        ${jumlahKelas === 0 ? `
                        <button type="button" class="btn btn-danger" onclick="executeDelete(${id})">
                            <i class="bi bi-trash"></i> Ya, Hapus Kurikulum
                        </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('deleteModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modal);

    // Show modal
    if (typeof $('#deleteModal').modal === 'function') {
        $('#deleteModal').modal('show');
    } else {
        // Fallback for basic confirmation
        if (jumlahKelas === 0) {
            if (confirm(`Apakah Anda yakin ingin menghapus kurikulum "${namaKurikulum}"?`)) {
                executeDelete(id);
            }
        } else {
            alert(`Kurikulum tidak dapat dihapus karena masih digunakan oleh ${jumlahKelas} kelas.`);
        }
    }
}

function executeDelete(id) {
    // Hide modal
    if (typeof $('#deleteModal').modal === 'function') {
        $('#deleteModal').modal('hide');
    }

    // Redirect to delete URL
    window.location.href = '/siswa-app/public/kurikulum/delete/' + id;
}
</script>
