-- =====================================================
-- SCRIPT PERBAIKAN SISTEM WALI KELAS
-- Sistem Informasi Akademik Siswa
-- =====================================================

USE siswa_app;

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. UPDATE ENUM ROLES UNTUK WALI KELAS
-- =====================================================

-- Update enum role untuk menambah wali_kelas dan role spesifik
ALTER TABLE users MODIFY COLUMN role ENUM(
    'admin', 'guru', 'staff', 'wali_kelas', 
    'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu',
    'wali_kpp', 'wali_x', 'wali_xi', 'wali_xii', 'wali_kpa'
) DEFAULT 'staff';

-- =====================================================
-- 2. UPDATE STRUKTUR TABEL KELAS
-- =====================================================

-- Pastikan kolom wali_kelas_id ada
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS wali_kelas_id INT NULL AFTER wali_kelas;

-- Tambah foreign key constraint jika belum ada
ALTER TABLE kelas ADD CONSTRAINT fk_kelas_wali_kelas 
FOREIGN KEY (wali_kelas_id) REFERENCES users(id) ON DELETE SET NULL;

-- Tambah index untuk performance
CREATE INDEX IF NOT EXISTS idx_wali_kelas_id ON kelas(wali_kelas_id);

-- =====================================================
-- 3. UPDATE STRUKTUR TABEL CATATAN_SISWA
-- =====================================================

-- Update enum jenis_catatan untuk menambah kategori wali kelas yang spesifik
ALTER TABLE catatan_siswa MODIFY COLUMN jenis_catatan ENUM(
    'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu',
    'wali_kpp_a', 'wali_kpp_b', 'wali_kpp_c',
    'wali_x_1', 'wali_x_2', 
    'wali_xi_1', 'wali_xi_2',
    'wali_xii_1', 'wali_xii_2',
    'wali_kpa',
    'bk_konseling', 'bk_pelanggaran', 'bk_prestasi', 'bk_lainnya',
    'akademik', 'prestasi', 'pelanggaran', 'kesehatan', 'kehadiran', 'umum'
) NOT NULL;

-- =====================================================
-- 4. UPDATE KATEGORI CATATAN
-- =====================================================

-- Hapus kategori lama yang tidak sesuai
DELETE FROM kategori_catatan WHERE kode_kategori IN ('wali_kpp', 'wali_x', 'wali_xi', 'wali_xii');

-- Insert kategori catatan baru untuk wali kelas spesifik
INSERT IGNORE INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class) VALUES
-- Wali Kelas KPP
('wali_kpp_a', 'Wali Kelas KPP A', 'Catatan Wali Kelas KPP A', '#6f42c1', 'bi-mortarboard'),
('wali_kpp_b', 'Wali Kelas KPP B', 'Catatan Wali Kelas KPP B', '#6f42c1', 'bi-mortarboard'),
('wali_kpp_c', 'Wali Kelas KPP C', 'Catatan Wali Kelas KPP C', '#6f42c1', 'bi-mortarboard'),

-- Wali Kelas X
('wali_x_1', 'Wali Kelas X-1', 'Catatan Wali Kelas X-1', '#007bff', 'bi-book'),
('wali_x_2', 'Wali Kelas X-2', 'Catatan Wali Kelas X-2', '#007bff', 'bi-book'),

-- Wali Kelas XI
('wali_xi_1', 'Wali Kelas XI-1', 'Catatan Wali Kelas XI-1', '#0d6efd', 'bi-journal'),
('wali_xi_2', 'Wali Kelas XI-2', 'Catatan Wali Kelas XI-2', '#0d6efd', 'bi-journal'),

-- Wali Kelas XII
('wali_xii_1', 'Wali Kelas XII-1', 'Catatan Wali Kelas XII-1', '#6610f2', 'bi-graduation'),
('wali_xii_2', 'Wali Kelas XII-2', 'Catatan Wali Kelas XII-2', '#6610f2', 'bi-graduation'),

-- Wali Kelas KPA
('wali_kpa', 'Wali Kelas KPA', 'Catatan Wali Kelas KPA', '#e83e8c', 'bi-award');

-- =====================================================
-- 5. CREATE/UPDATE USER_KELAS_MAPPING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS user_kelas_mapping (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    kelas_id INT NOT NULL,
    role_type ENUM('wali_kelas', 'guru_mapel', 'guru_piket') DEFAULT 'wali_kelas',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (kelas_id) REFERENCES kelas(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_kelas_role (user_id, kelas_id, role_type),
    INDEX idx_user_id (user_id),
    INDEX idx_kelas_id (kelas_id),
    INDEX idx_role_type (role_type)
);

-- =====================================================
-- 6. UPDATE DATA KELAS SESUAI STRUKTUR BARU
-- =====================================================

-- Update kelas yang sudah ada untuk menyesuaikan dengan struktur baru
UPDATE kelas SET nama_kelas = 'KPP A', tingkat = 'KPP' WHERE nama_kelas LIKE '%KPP%' AND nama_kelas LIKE '%A%';
UPDATE kelas SET nama_kelas = 'KPP B', tingkat = 'KPP' WHERE nama_kelas LIKE '%KPP%' AND nama_kelas LIKE '%B%';
UPDATE kelas SET nama_kelas = 'KPP C', tingkat = 'KPP' WHERE nama_kelas LIKE '%KPP%' AND nama_kelas LIKE '%C%';

-- Insert kelas baru jika belum ada
INSERT IGNORE INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, kapasitas, is_active, created_by) VALUES
('KPP A', 'KPP', 'Umum', '2024/2025', 30, 1, 1),
('KPP B', 'KPP', 'Umum', '2024/2025', 30, 1, 1),
('KPP C', 'KPP', 'Umum', '2024/2025', 30, 1, 1),
('X-1', 'X', 'Umum', '2024/2025', 30, 1, 1),
('X-2', 'X', 'Umum', '2024/2025', 30, 1, 1),
('XI-1', 'XI', 'Umum', '2024/2025', 30, 1, 1),
('XI-2', 'XI', 'Umum', '2024/2025', 30, 1, 1),
('XII-1', 'XII', 'Umum', '2024/2025', 30, 1, 1),
('XII-2', 'XII', 'Umum', '2024/2025', 30, 1, 1),
('KPA', 'KPA', 'Umum', '2024/2025', 30, 1, 1);

-- =====================================================
-- 7. CREATE SAMPLE WALI KELAS USERS
-- =====================================================

-- Insert sample wali kelas users
INSERT IGNORE INTO users (username, email, password, role, nama_lengkap, is_active) VALUES
('wali_kpp_a', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas KPP A', 1),
('wali_kpp_b', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas KPP B', 1),
('wali_kpp_c', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas KPP C', 1),
('wali_x_1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas X-1', 1),
('wali_x_2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas X-2', 1),
('wali_xi_1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas XI-1', 1),
('wali_xi_2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas XI-2', 1),
('wali_xii_1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas XII-1', 1),
('wali_xii_2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas XII-2', 1),
('wali_kpa', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'wali_kelas', 'Wali Kelas KPA', 1);

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 8. VERIFICATION
-- =====================================================

SELECT 'Wali Kelas System Fix completed successfully!' as status;

-- Show updated kelas
SELECT 'Updated Kelas:' as info;
SELECT id, nama_kelas, tingkat, wali_kelas, wali_kelas_id FROM kelas ORDER BY 
    CASE tingkat 
        WHEN 'KPP' THEN 1 
        WHEN 'X' THEN 2 
        WHEN 'XI' THEN 3 
        WHEN 'XII' THEN 4 
        WHEN 'KPA' THEN 5 
        ELSE 6 
    END, nama_kelas;

-- Show wali kelas users
SELECT 'Wali Kelas Users:' as info;
SELECT id, username, nama_lengkap, role FROM users WHERE role = 'wali_kelas';

-- Show kategori catatan for wali kelas
SELECT 'Wali Kelas Categories:' as info;
SELECT kode_kategori, nama_kategori FROM kategori_catatan WHERE kode_kategori LIKE 'wali_%';
