<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-building text-primary"></i>
                        <?= isset($kelas) ? 'Edit Kelas' : 'Tambah Kelas Baru' ?>
                    </h1>
                    <p class="text-muted mb-0">
                        <?= isset($kelas) ? 'Perbarui informasi kelas' : 'Lengkapi form di bawah untuk menambahkan kelas baru' ?>
                    </p>
                </div>
                <div>
                    <a href="/siswa-app/public/kelas" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Error!</strong> <?= htmlspecialchars($_SESSION['error']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['error']); endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i>
        <strong>Berhasil!</strong> <?= htmlspecialchars($_SESSION['success']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['success']); endif; ?>

    <!-- Main Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-building"></i>
                        <?= isset($kelas) ? 'Form Edit Kelas' : 'Form Tambah Kelas' ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= isset($kelas) ? '/siswa-app/public/kelas/edit/' . $kelas['id_kelas'] : '/siswa-app/public/kelas/create' ?>" id="formKelas" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">

                        <!-- Basic Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="bi bi-info-circle text-primary"></i>
                                Informasi Dasar Kelas
                            </h6>
                            <hr class="section-divider">

                            <div class="row">
                                <!-- Nama Kelas -->
                                <div class="col-md-6 mb-3">
                                    <label for="nama_kelas" class="form-label required">
                                        <i class="bi bi-tag"></i>
                                        Nama Kelas
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="nama_kelas"
                                           name="nama_kelas"
                                           value="<?= htmlspecialchars($kelas['nama_kelas'] ?? '') ?>"
                                           placeholder="Contoh: KPP A, X-1, XI-2"
                                           required
                                           autocomplete="off">
                                    <div class="invalid-feedback">
                                        Nama kelas harus diisi
                                    </div>
                                    <small class="form-text text-muted">
                                        Masukkan nama kelas yang unik dan mudah diidentifikasi
                                    </small>
                                </div>

                                <!-- Tingkat -->
                                <div class="col-md-6 mb-3">
                                    <label for="tingkat" class="form-label required">
                                        <i class="bi bi-layers"></i>
                                        Tingkat
                                    </label>
                                    <select class="form-control" id="tingkat" name="tingkat" required>
                                        <option value="">-- Pilih Tingkat --</option>
                                        <option value="KPP" <?= (isset($kelas) && $kelas['tingkat'] == 'KPP') ? 'selected' : '' ?>>
                                            KPP - Kelas Persiapan Pertama
                                        </option>
                                        <option value="X" <?= (isset($kelas) && $kelas['tingkat'] == 'X') ? 'selected' : '' ?>>
                                            X - Kelas 10
                                        </option>
                                        <option value="XI" <?= (isset($kelas) && $kelas['tingkat'] == 'XI') ? 'selected' : '' ?>>
                                            XI - Kelas 11
                                        </option>
                                        <option value="XII" <?= (isset($kelas) && $kelas['tingkat'] == 'XII') ? 'selected' : '' ?>>
                                            XII - Kelas 12
                                        </option>
                                        <option value="KPA" <?= (isset($kelas) && $kelas['tingkat'] == 'KPA') ? 'selected' : '' ?>>
                                            KPA - Kelas Persiapan Atas
                                        </option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Tingkat kelas harus dipilih
                                    </div>
                                    <small class="form-text text-muted">
                                        Pilih tingkat sesuai dengan jenjang pendidikan
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Academic Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="bi bi-book text-success"></i>
                                Informasi Akademik
                            </h6>
                            <hr class="section-divider">

                            <div class="row">
                                <!-- Kurikulum -->
                                <div class="col-md-6 mb-3">
                                    <label for="kurikulum_id" class="form-label">
                                        <i class="bi bi-book-half"></i>
                                        Kurikulum
                                    </label>
                                    <select class="form-control" id="kurikulum_id" name="kurikulum_id">
                                        <option value="">-- Pilih Kurikulum (Opsional) --</option>
                                        <?php if (isset($kurikulum) && !empty($kurikulum)): ?>
                                            <?php foreach ($kurikulum as $k): ?>
                                                <option value="<?= $k['id_kurikulum'] ?>"
                                                        <?= (isset($kelas) && $kelas['kurikulum_id'] == $k['id_kurikulum']) ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($k['nama_kurikulum']) ?> (<?= htmlspecialchars($k['kode_kurikulum']) ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <option value="" disabled>Tidak ada kurikulum tersedia</option>
                                        <?php endif; ?>
                                    </select>
                                    <small class="form-text text-muted">
                                        Pilih kurikulum yang akan digunakan untuk kelas ini
                                    </small>
                                </div>

                                <!-- Tahun Pelajaran -->
                                <div class="col-md-6 mb-3">
                                    <label for="tahun_pelajaran" class="form-label required">
                                        <i class="bi bi-calendar"></i>
                                        Tahun Pelajaran
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="tahun_pelajaran"
                                           name="tahun_pelajaran"
                                           value="<?= htmlspecialchars($kelas['tahun_pelajaran'] ?? '2024/2025') ?>"
                                           placeholder="2024/2025"
                                           pattern="[0-9]{4}/[0-9]{4}"
                                           required>
                                    <div class="invalid-feedback">
                                        Tahun pelajaran harus diisi dengan format YYYY/YYYY
                                    </div>
                                    <small class="form-text text-muted">
                                        Format: YYYY/YYYY (contoh: 2024/2025)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Management Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="bi bi-people text-info"></i>
                                Informasi Pengelolaan
                            </h6>
                            <hr class="section-divider">

                            <div class="row">
                                <!-- Wali Kelas -->
                                <div class="col-md-6 mb-3">
                                    <label for="wali_kelas" class="form-label">
                                        <i class="bi bi-person"></i>
                                        Wali Kelas
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="wali_kelas"
                                           name="wali_kelas"
                                           value="<?= htmlspecialchars($kelas['wali_kelas'] ?? '') ?>"
                                           placeholder="Nama Wali Kelas"
                                           autocomplete="name">
                                    <small class="form-text text-muted">
                                        Nama guru yang bertanggung jawab sebagai wali kelas
                                    </small>
                                </div>

                                <!-- Kapasitas -->
                                <div class="col-md-6 mb-3">
                                    <label for="kapasitas" class="form-label">
                                        <i class="bi bi-people"></i>
                                        Kapasitas Siswa
                                    </label>
                                    <input type="number"
                                           class="form-control"
                                           id="kapasitas"
                                           name="kapasitas"
                                           value="<?= htmlspecialchars($kelas['kapasitas'] ?? '30') ?>"
                                           min="1"
                                           max="50"
                                           placeholder="30">
                                    <small class="form-text text-muted">
                                        Jumlah maksimal siswa dalam kelas (1-50)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <hr>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        Field dengan tanda <span class="text-danger">*</span> wajib diisi
                                    </small>
                                </div>
                                <div>
                                    <a href="/siswa-app/public/kelas" class="btn btn-secondary mr-2">
                                        <i class="bi bi-x-circle"></i>
                                        Batal
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="btnSubmit">
                                        <i class="bi bi-check-circle"></i>
                                        <span class="btn-text">
                                            <?= isset($kelas) ? 'Update Kelas' : 'Simpan Kelas' ?>
                                        </span>
                                        <span class="btn-loading d-none">
                                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            Menyimpan...
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
/* Form Styling */
.form-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
    animation: fadeInUp 0.5s ease-out;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.section-divider {
    margin: 0.75rem 0 1.5rem 0;
    border-color: #dee2e6;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.form-text {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Button Styling */
.btn {
    border-radius: 0.375rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-secondary:hover {
    background-color: #545b62;
    border-color: #545b62;
    transform: translateY(-1px);
}

/* Card Styling */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 0.75rem 0.75rem 0 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.125);
}

/* Loading State */
.btn-loading .btn-text {
    display: none;
}

.btn:not(.btn-loading) .btn-loading {
    display: none !important;
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions .d-flex {
        flex-direction: column;
        align-items: stretch !important;
    }

    .form-actions .btn {
        margin-bottom: 0.5rem;
    }
}
</style>

<!-- Custom JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('formKelas');
    const submitBtn = document.getElementById('btnSubmit');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');

    // Form validation
    function validateForm() {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            const value = field.value.trim();

            if (!value) {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        });

        return isValid;
    }

    // Real-time validation
    form.addEventListener('input', function(e) {
        const field = e.target;
        if (field.hasAttribute('required')) {
            const value = field.value.trim();

            if (value) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            } else {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
            }
        }
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!validateForm()) {
            // Scroll to first invalid field
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                firstInvalid.focus();
            }
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.classList.add('btn-loading');
        btnText.style.display = 'none';
        btnLoading.classList.remove('d-none');

        // Submit form
        setTimeout(() => {
            form.submit();
        }, 500);
    });

    // Auto-format tahun pelajaran
    const tahunPelajaranField = document.getElementById('tahun_pelajaran');
    tahunPelajaranField.addEventListener('input', function(e) {
        let value = e.target.value.replace(/[^0-9]/g, '');
        if (value.length >= 4) {
            let year1 = value.substring(0, 4);
            let year2 = value.substring(4, 8);
            if (year2.length > 0) {
                e.target.value = year1 + '/' + year2;
            } else {
                e.target.value = year1;
            }
        }
    });

    // Auto-dismiss alerts
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success')) {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }
        });
    }, 5000);

    // Enhanced select styling
    const selectElements = document.querySelectorAll('select.form-control');
    selectElements.forEach(select => {
        select.addEventListener('change', function() {
            if (this.value) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });

    // Form reset functionality
    const cancelBtn = document.querySelector('a[href*="/kelas"]');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function(e) {
            const hasChanges = Array.from(form.elements).some(element => {
                return element.value && element.value.trim() !== '';
            });

            if (hasChanges) {
                if (!confirm('Anda memiliki perubahan yang belum disimpan. Yakin ingin keluar?')) {
                    e.preventDefault();
                }
            }
        });
    }
});
</script>