-- =====================================================
-- SCRIPT PERBAIKAN DATABASE ISSUES
-- Sistem Informasi Akademik Siswa
-- =====================================================

USE siswa_app;

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. CREATE MISSING TABLES
-- =====================================================

-- Table: users (pastikan ada)
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'guru', 'staff', 'wali_kelas', 'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu') DEFAULT 'staff',
    nama_lengkap VARCHAR(100) NOT NULL,
    foto_profil VARCHAR(255),
    no_telepon VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    password_reset_token VARCHAR(255) NULL,
    password_reset_expires TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- Table: kelas
CREATE TABLE IF NOT EXISTS kelas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama_kelas VARCHAR(20) NOT NULL,
    tingkat INT NOT NULL,
    jurusan VARCHAR(50),
    tahun_pelajaran VARCHAR(9) NOT NULL,
    wali_kelas VARCHAR(100),
    wali_kelas_id INT NULL,
    kapasitas INT DEFAULT 30,
    kurikulum_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    FOREIGN KEY (wali_kelas_id) REFERENCES users(id),
    INDEX idx_tingkat (tingkat),
    INDEX idx_tahun (tahun_pelajaran),
    INDEX idx_active (is_active),
    INDEX idx_wali_kelas (wali_kelas_id)
);

-- Table: siswa
CREATE TABLE IF NOT EXISTS siswa (
    id_siswa INT PRIMARY KEY AUTO_INCREMENT,
    nis VARCHAR(20) UNIQUE NOT NULL,
    nisn VARCHAR(20) UNIQUE,
    nama_lengkap VARCHAR(100) NOT NULL,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    tempat_lahir VARCHAR(50),
    tanggal_lahir DATE,
    alamat TEXT,
    no_telepon VARCHAR(20),
    email VARCHAR(100),
    nama_ayah VARCHAR(100),
    nama_ibu VARCHAR(100),
    pekerjaan_ayah VARCHAR(50),
    pekerjaan_ibu VARCHAR(50),
    kelas_id INT,
    tahun_masuk YEAR,
    status_siswa ENUM('aktif', 'lulus', 'mutasi', 'dropout') DEFAULT 'aktif',
    foto VARCHAR(255),
    created_by INT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (kelas_id) REFERENCES kelas(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_nis (nis),
    INDEX idx_nama (nama_lengkap),
    INDEX idx_status (status_siswa),
    INDEX idx_kelas (kelas_id)
);

-- Table: kategori_catatan
CREATE TABLE IF NOT EXISTS kategori_catatan (
    id INT PRIMARY KEY AUTO_INCREMENT,
    kode_kategori VARCHAR(20) NOT NULL UNIQUE,
    nama_kategori VARCHAR(100) NOT NULL,
    deskripsi TEXT,
    warna_badge VARCHAR(7) DEFAULT '#6c757d',
    icon_class VARCHAR(50) DEFAULT 'bi-note-text',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_kode (kode_kategori),
    INDEX idx_active (is_active)
);

-- Table: catatan_siswa
CREATE TABLE IF NOT EXISTS catatan_siswa (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    jenis_catatan ENUM(
        'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu',
        'wali_kpp', 'wali_x', 'wali_xi', 'wali_xii', 'wali_kpa',
        'bk_konseling', 'bk_pelanggaran', 'bk_prestasi', 'bk_lainnya',
        'akademik', 'prestasi', 'pelanggaran', 'kesehatan', 'kehadiran', 'umum'
    ) NOT NULL,
    judul_catatan VARCHAR(255) NOT NULL,
    isi_catatan TEXT NOT NULL,
    tanggal_catatan DATE NOT NULL,
    tingkat_prioritas ENUM('rendah', 'sedang', 'tinggi', 'urgent') DEFAULT 'sedang',
    status_catatan ENUM('draft', 'aktif', 'selesai', 'ditunda') DEFAULT 'aktif',
    tindak_lanjut TEXT,
    tanggal_tindak_lanjut DATE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_siswa_jenis (siswa_id, jenis_catatan),
    INDEX idx_tanggal (tanggal_catatan),
    INDEX idx_status (status_catatan),
    INDEX idx_prioritas (tingkat_prioritas)
);

-- Table: berkas
CREATE TABLE IF NOT EXISTS berkas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    jenis_berkas ENUM(
        'kartu_keluarga', 'akta_lahir',
        'rapor_kelas_x', 'rapor_kelas_xi', 'rapor_kelas_xii',
        'ijazah_sd', 'ijazah_smp', 'ijazah_sma',
        'foto_siswa', 'surat_keterangan_sehat', 'surat_kelakuan_baik',
        'piagam_prestasi', 'lainnya'
    ) NOT NULL,
    nama_berkas VARCHAR(255) NOT NULL,
    nama_file_asli VARCHAR(255) NOT NULL,
    nama_file_sistem VARCHAR(255) NOT NULL,
    ukuran_file INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    keterangan TEXT,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    INDEX idx_siswa (siswa_id),
    INDEX idx_jenis (jenis_berkas),
    INDEX idx_hash (file_hash)
);

-- Table: absensi
CREATE TABLE IF NOT EXISTS absensi (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    tanggal DATE NOT NULL,
    jenis_ketidakhadiran ENUM('sakit', 'ijin', 'alpha') NOT NULL,
    keterangan TEXT NULL,
    surat_keterangan VARCHAR(255) NULL,
    jam_masuk TIME NULL,
    jam_keluar TIME NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_siswa_tanggal (siswa_id, tanggal),
    INDEX idx_tanggal (tanggal),
    INDEX idx_jenis (jenis_ketidakhadiran),
    UNIQUE KEY unique_siswa_tanggal (siswa_id, tanggal)
);

-- Table: kurikulum
CREATE TABLE IF NOT EXISTS kurikulum (
    id_kurikulum INT PRIMARY KEY AUTO_INCREMENT,
    nama_kurikulum VARCHAR(100) NOT NULL,
    kode_kurikulum VARCHAR(20) UNIQUE NOT NULL,
    tahun_berlaku YEAR NOT NULL,
    deskripsi TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_kode (kode_kurikulum),
    INDEX idx_tahun (tahun_berlaku),
    INDEX idx_active (is_active)
);

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 2. INSERT DEFAULT DATA
-- =====================================================

-- Insert default admin user if not exists
INSERT IGNORE INTO users (username, email, password, role, nama_lengkap, is_active) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Administrator', TRUE);

-- Insert default kategori catatan
INSERT IGNORE INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class) VALUES
('pamong_mp', 'Pamong MP', 'Catatan Pamong Masa Persiapan', '#17a2b8', 'bi-person-badge'),
('pamong_mt', 'Pamong MT', 'Catatan Pamong Masa Transisi', '#28a745', 'bi-person-check'),
('pamong_mm', 'Pamong MM', 'Catatan Pamong Masa Mandiri', '#ffc107', 'bi-person-gear'),
('pamong_mu', 'Pamong MU', 'Catatan Pamong Masa Uji', '#fd7e14', 'bi-person-exclamation'),
('wali_kpp', 'Wali Kelas KPP', 'Catatan Wali Kelas Persiapan Profesi', '#6f42c1', 'bi-mortarboard'),
('wali_x', 'Wali Kelas X', 'Catatan Wali Kelas X', '#007bff', 'bi-book'),
('wali_xi', 'Wali Kelas XI', 'Catatan Wali Kelas XI', '#0d6efd', 'bi-journal'),
('wali_xii', 'Wali Kelas XII', 'Catatan Wali Kelas XII', '#6610f2', 'bi-graduation'),
('wali_kpa', 'Wali Kelas KPA', 'Catatan Wali Kelas Pasca', '#e83e8c', 'bi-award'),
('bk_konseling', 'BK Konseling', 'Catatan Bimbingan Konseling', '#dc3545', 'bi-heart'),
('bk_pelanggaran', 'BK Pelanggaran', 'Catatan Pelanggaran Siswa', '#dc3545', 'bi-exclamation-triangle'),
('bk_prestasi', 'BK Prestasi', 'Catatan Prestasi Siswa', '#198754', 'bi-trophy'),
('bk_lainnya', 'BK Lainnya', 'Catatan BK Lainnya', '#6c757d', 'bi-chat-dots'),
('akademik', 'Akademik', 'Catatan terkait prestasi dan masalah akademik', '#007bff', 'bi-book'),
('prestasi', 'Prestasi', 'Catatan prestasi siswa', '#28a745', 'bi-trophy'),
('pelanggaran', 'Pelanggaran', 'Catatan pelanggaran dan tindakan disiplin', '#dc3545', 'bi-exclamation-triangle'),
('kesehatan', 'Kesehatan', 'Catatan kesehatan siswa', '#17a2b8', 'bi-heart-pulse'),
('kehadiran', 'Kehadiran', 'Catatan kehadiran dan ketidakhadiran', '#ffc107', 'bi-calendar-check'),
('umum', 'Umum', 'Catatan umum lainnya', '#6c757d', 'bi-note-text');

-- Insert sample kurikulum
INSERT IGNORE INTO kurikulum (nama_kurikulum, kode_kurikulum, tahun_berlaku, deskripsi, created_by) VALUES
('Kurikulum 2013', 'K13', 2013, 'Kurikulum 2013 untuk pendidikan menengah', 1),
('Kurikulum Merdeka', 'KM', 2022, 'Kurikulum Merdeka untuk pendidikan menengah', 1);

-- Insert sample classes
INSERT IGNORE INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, kurikulum_id, created_by) VALUES 
('X-IPA-1', 10, 'IPA', '2024/2025', 'Budi Santoso', 2, 1),
('X-IPS-1', 10, 'IPS', '2024/2025', 'Siti Aminah', 2, 1),
('XI-IPA-1', 11, 'IPA', '2024/2025', 'Ahmad Rahman', 2, 1),
('XI-IPS-1', 11, 'IPS', '2024/2025', 'Dewi Sartika', 2, 1),
('XII-IPA-1', 12, 'IPA', '2024/2025', 'Joko Widodo', 1, 1),
('XII-IPS-1', 12, 'IPS', '2024/2025', 'Susi Susanti', 1, 1);

-- Insert sample students
INSERT IGNORE INTO siswa (nis, nisn, nama_lengkap, jenis_kelamin, tempat_lahir, tanggal_lahir, alamat, kelas_id, tahun_masuk, created_by) VALUES 
('2024001', '1234567890', 'Ahmad Fauzi', 'L', 'Jakarta', '2007-05-15', 'Jl. Merdeka No. 123, Jakarta', 1, 2024, 1),
('2024002', '1234567891', 'Siti Nurhaliza', 'P', 'Bandung', '2007-08-20', 'Jl. Sudirman No. 456, Bandung', 1, 2024, 1),
('2024003', '1234567892', 'Budi Santoso', 'L', 'Surabaya', '2007-03-10', 'Jl. Pahlawan No. 789, Surabaya', 2, 2024, 1),
('2023001', '1234567893', 'Dewi Sartika', 'P', 'Yogyakarta', '2006-12-05', 'Jl. Malioboro No. 321, Yogyakarta', 3, 2023, 1),
('2023002', '1234567894', 'Rudi Hermawan', 'L', 'Medan', '2006-09-18', 'Jl. Gatot Subroto No. 654, Medan', 4, 2023, 1);

-- =====================================================
-- 3. VERIFICATION
-- =====================================================

-- Verify tables created
SELECT 'Database fix completed successfully!' as status;

SELECT 
    TABLE_NAME, 
    TABLE_ROWS,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'siswa_app' 
ORDER BY TABLE_NAME;

-- Show sample data
SELECT 'Sample Users:' as info;
SELECT id, username, nama_lengkap, role, is_active FROM users LIMIT 5;

SELECT 'Sample Kelas:' as info;
SELECT id, nama_kelas, tingkat, tahun_pelajaran FROM kelas LIMIT 5;

SELECT 'Sample Siswa:' as info;
SELECT id_siswa, nis, nama_lengkap, jenis_kelamin FROM siswa LIMIT 5;

SELECT 'Kategori Catatan:' as info;
SELECT COUNT(*) as total_kategori FROM kategori_catatan;
