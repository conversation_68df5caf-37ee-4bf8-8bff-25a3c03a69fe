<?php
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../models/Kelas.php';
require_once __DIR__ . '/../models/Berkas.php';
require_once __DIR__ . '/../helpers/Security.php';

class DashboardController {
    private $siswaModel;
    private $kelasModel;
    private $berkasModel;
    
    public function __construct() {
        $this->siswaModel = new Siswa();
        $this->kelasModel = new Kelas();
        $this->berkasModel = new Berkas();
    }
    
    /**
     * Dashboard index
     */
    public function index() {
        try {
            // Get statistics
            $stats = $this->getStatistics();

            $data = [
                'title' => 'Dashboard',
                'stats' => $stats,
                'csrf_token' => Security::generateCSRFToken()
            ];

            // Use main dashboard view
            $this->view('dashboard/index', $data);

        } catch (Exception $e) {
            // Fallback to basic dashboard
            $data = [
                'title' => 'Dashboard',
                'stats' => [
                    'total_siswa' => 0,
                    'total_kelas' => 0,
                    'siswa_aktif' => 0,
                    'kelas_aktif' => 0
                ],
                'csrf_token' => Security::generateCSRFToken()
            ];

            $this->view('dashboard/index', $data);
        }
    }
    
    /**
     * Get system statistics based on user role
     */
    private function getStatistics() {
        // Get filtered data based on user role
        $allSiswa = $this->getFilteredSiswa();
        $allKelas = $this->getFilteredKelas();

        // Count students by status
        $siswaAktif = array_filter($allSiswa, function($siswa) {
            return ($siswa['status_siswa'] ?? 'aktif') === 'aktif';
        });

        $siswaLulus = array_filter($allSiswa, function($siswa) {
            return ($siswa['status_siswa'] ?? 'aktif') === 'lulus';
        });

        $siswaMutasi = array_filter($allSiswa, function($siswa) {
            return ($siswa['status_siswa'] ?? 'aktif') === 'mutasi';
        });

        // Count files
        $totalBerkas = 0;
        foreach ($allSiswa as $siswa) {
            $berkas = $this->berkasModel->getBySiswaId($siswa['id'] ?? $siswa['id_siswa']);
            $totalBerkas += count($berkas);
        }

        // Count active classes
        $kelasAktif = array_filter($allKelas, function($kelas) {
            return ($kelas['is_active'] ?? true) == true;
        });

        return [
            'total_siswa' => count($allSiswa),
            'siswa_aktif' => count($siswaAktif),
            'siswa_lulus' => count($siswaLulus),
            'siswa_mutasi' => count($siswaMutasi),
            'total_kelas' => count($allKelas),
            'kelas_aktif' => count($kelasAktif),
            'total_berkas' => $totalBerkas,
            'user_role_info' => $this->getUserRoleInfo()
        ];
    }

    /**
     * Get filtered siswa based on user role
     */
    private function getFilteredSiswa() {
        $userRole = $_SESSION['user_role'] ?? '';

        // Admin and staff can see all students
        if ($userRole === 'admin' || Security::isStaff()) {
            return $this->siswaModel->getAll();
        }

        // Wali kelas can see their class students
        if (Security::isWaliKelas()) {
            return $this->siswaModel->getByWaliKelas($_SESSION['user_id']);
        }

        // Pamong can see students based on their tingkat
        if (Security::isPamong()) {
            return $this->getSiswaByPamongTingkat();
        }

        return [];
    }

    /**
     * Get filtered kelas based on user role
     */
    private function getFilteredKelas() {
        $userRole = $_SESSION['user_role'] ?? '';

        // Admin and staff can see all classes
        if ($userRole === 'admin' || Security::isStaff()) {
            return $this->kelasModel->getAll();
        }

        // Wali kelas can see their class
        if (Security::isWaliKelas()) {
            return $this->kelasModel->getByWaliKelas($_SESSION['user_id']);
        }

        // Pamong can see classes based on their tingkat
        if (Security::isPamong()) {
            return $this->getKelasByPamongTingkat();
        }

        return [];
    }

    /**
     * Get siswa by pamong tingkat
     */
    private function getSiswaByPamongTingkat() {
        $pamongType = Security::getPamongType();
        $allowedTingkat = [];

        switch ($pamongType) {
            case 'mp':
                $allowedTingkat = ['KPP'];
                break;
            case 'mt':
                $allowedTingkat = ['X'];
                break;
            case 'mm':
                $allowedTingkat = ['XI'];
                break;
            case 'mu':
                $allowedTingkat = ['XII', 'KPA'];
                break;
            default:
                return [];
        }

        return $this->siswaModel->getByTingkatKelas($allowedTingkat);
    }

    /**
     * Get kelas by pamong tingkat
     */
    private function getKelasByPamongTingkat() {
        $pamongType = Security::getPamongType();
        $allowedTingkat = [];

        switch ($pamongType) {
            case 'mp':
                $allowedTingkat = ['KPP'];
                break;
            case 'mt':
                $allowedTingkat = ['X'];
                break;
            case 'mm':
                $allowedTingkat = ['XI'];
                break;
            case 'mu':
                $allowedTingkat = ['XII', 'KPA'];
                break;
            default:
                return [];
        }

        return $this->kelasModel->getByTingkat($allowedTingkat);
    }

    /**
     * Get user role information for display
     */
    private function getUserRoleInfo() {
        $userRole = $_SESSION['user_role'] ?? '';
        $userName = $_SESSION['user_name'] ?? $_SESSION['username'] ?? 'User';

        $roleInfo = [
            'role' => $userRole,
            'name' => $userName,
            'description' => 'User'
        ];

        if ($userRole === 'admin') {
            $roleInfo['description'] = 'Administrator - Akses penuh ke semua data';
        } elseif (Security::isStaff()) {
            $roleInfo['description'] = 'Staff - Akses baca ke semua data';
        } elseif (Security::isWaliKelas()) {
            $roleInfo['description'] = 'Wali Kelas - Akses ke siswa dalam kelas yang diampu';
        } elseif (Security::isPamong()) {
            $pamongType = Security::getPamongType();
            $tingkatMap = [
                'mp' => 'KPP',
                'mt' => 'X',
                'mm' => 'XI',
                'mu' => 'XII & KPA'
            ];
            $tingkat = $tingkatMap[$pamongType] ?? 'Unknown';
            $roleInfo['description'] = "Pamong {$tingkat} - Akses ke siswa tingkat {$tingkat}";
        }

        return $roleInfo;
    }
    
    /**
     * Get recent activities (mock data for now)
     */
    private function getRecentActivities() {
        // In a real application, you would fetch this from an activity log table
        return [
            [
                'type' => 'siswa_added',
                'message' => 'Siswa baru ditambahkan: Ahmad Fauzi',
                'time' => '2 jam yang lalu',
                'icon' => 'bi-person-plus',
                'color' => 'success'
            ],
            [
                'type' => 'berkas_uploaded',
                'message' => 'Berkas kartu keluarga diupload untuk Siti Nurhaliza',
                'time' => '4 jam yang lalu',
                'icon' => 'bi-file-earmark-arrow-up',
                'color' => 'info'
            ],
            [
                'type' => 'kelas_updated',
                'message' => 'Data kelas X-IPA-1 diperbarui',
                'time' => '1 hari yang lalu',
                'icon' => 'bi-building',
                'color' => 'warning'
            ],
            [
                'type' => 'user_login',
                'message' => 'User admin melakukan login',
                'time' => '2 hari yang lalu',
                'icon' => 'bi-box-arrow-in-right',
                'color' => 'primary'
            ]
        ];
    }
    
    /**
     * Get chart data
     */
    private function getChartData() {
        $allSiswa = $this->siswaModel->getAll();
        $allKelas = $this->kelasModel->getAll();
        
        // Students per class
        $siswaPerKelas = [];
        foreach ($allKelas as $kelas) {
            $kelasId = $kelas['id'] ?? $kelas['id_kelas'];
            $namaKelas = $kelas['nama_kelas'];
            
            $count = 0;
            foreach ($allSiswa as $siswa) {
                if (($siswa['kelas_id'] ?? $siswa['id_kelas']) == $kelasId) {
                    $count++;
                }
            }
            
            $siswaPerKelas[] = [
                'label' => $namaKelas,
                'count' => $count
            ];
        }
        
        // Students by gender
        $siswaByGender = [
            'L' => 0,
            'P' => 0
        ];
        
        foreach ($allSiswa as $siswa) {
            $gender = $siswa['jenis_kelamin'] ?? 'L';
            if (isset($siswaByGender[$gender])) {
                $siswaByGender[$gender]++;
            }
        }
        
        // Students by status
        $siswaByStatus = [
            'aktif' => 0,
            'lulus' => 0,
            'mutasi' => 0,
            'dropout' => 0
        ];
        
        foreach ($allSiswa as $siswa) {
            $status = $siswa['status_siswa'] ?? 'aktif';
            if (isset($siswaByStatus[$status])) {
                $siswaByStatus[$status]++;
            }
        }
        
        return [
            'siswa_per_kelas' => $siswaPerKelas,
            'siswa_by_gender' => $siswaByGender,
            'siswa_by_status' => $siswaByStatus
        ];
    }
    
    /**
     * Get quick stats for AJAX
     */
    public function getQuickStats() {
        header('Content-Type: application/json');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        Security::requireAuth();
        
        $stats = $this->getStatistics();
        echo json_encode($stats);
    }
    
    /**
     * Get chart data for AJAX
     */
    public function getChartDataAjax() {
        header('Content-Type: application/json');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        Security::requireAuth();
        
        $chartData = $this->getChartData();
        echo json_encode($chartData);
    }
    
    /**
     * Render view
     */
    private function view($path, $data = []) {
        extract($data);
        require_once __DIR__ . '/../views/layouts/header.php';
        require_once __DIR__ . '/../views/' . $path . '.php';
        require_once __DIR__ . '/../views/layouts/footer.php';
    }
}
