<?php
/**
 * Script untuk memperbaiki issues database dan aplikasi
 * Jalankan script ini untuk memperbaiki masalah yang ditemukan
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Perbaikan Issues Aplikasi Siswa</h1>\n";
echo "<pre>\n";

// Include database config
require_once __DIR__ . '/app/config/db.php';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Koneksi database berhasil\n";
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE " . DB_NAME);
    
    echo "✅ Database '" . DB_NAME . "' siap\n";
    
    // Read and execute fix script
    $sqlFile = __DIR__ . '/database/fix_database_issues.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("File SQL tidak ditemukan: " . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "📄 Menjalankan " . count($statements) . " statement SQL...\n\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        try {
            if (trim($statement)) {
                $pdo->exec($statement);
                
                // Show progress for important statements
                if (stripos($statement, 'CREATE TABLE') !== false) {
                    preg_match('/CREATE TABLE.*?(\w+)/i', $statement, $matches);
                    $tableName = $matches[1] ?? 'unknown';
                    echo "✅ Tabel '$tableName' berhasil dibuat/diverifikasi\n";
                } elseif (stripos($statement, 'INSERT') !== false) {
                    $affectedRows = $pdo->lastInsertId();
                    echo "📝 Data sample berhasil diinsert\n";
                } elseif (stripos($statement, 'SELECT') !== false && stripos($statement, 'info') !== false) {
                    $result = $pdo->query($statement);
                    if ($result) {
                        $row = $result->fetch(PDO::FETCH_ASSOC);
                        if ($row) {
                            echo "ℹ️  " . implode(' | ', $row) . "\n";
                        }
                    }
                }
                
                $successCount++;
            }
        } catch (PDOException $e) {
            echo "❌ Error pada statement " . ($index + 1) . ": " . $e->getMessage() . "\n";
            $errorCount++;
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "📊 RINGKASAN PERBAIKAN DATABASE:\n";
    echo "✅ Berhasil: $successCount statement\n";
    echo "❌ Error: $errorCount statement\n";
    
    // Verify tables exist
    echo "\n🔍 VERIFIKASI TABEL:\n";
    $tables = ['users', 'kelas', 'siswa', 'catatan_siswa', 'kategori_catatan', 'berkas', 'absensi'];
    
    foreach ($tables as $table) {
        try {
            $result = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
            echo "✅ Tabel '$table': $count record\n";
        } catch (PDOException $e) {
            echo "❌ Tabel '$table': TIDAK DITEMUKAN\n";
        }
    }
    
    // Check for missing view files
    echo "\n🔍 VERIFIKASI FILE VIEW:\n";
    $viewFiles = [
        'app/views/auth/profile.php',
        'app/views/auth/users.php', 
        'app/views/auth/unauthorized.php',
        'app/views/admin/user_edit.php',
        'app/views/absensi/edit.php'
    ];
    
    foreach ($viewFiles as $file) {
        if (file_exists(__DIR__ . '/' . $file)) {
            echo "✅ File '$file': ADA\n";
        } else {
            echo "❌ File '$file': TIDAK DITEMUKAN\n";
        }
    }
    
    // Test database connection with app config
    echo "\n🔍 TEST KONEKSI APLIKASI:\n";
    try {
        $appPdo = getDBConnection();
        echo "✅ Koneksi aplikasi berhasil\n";
        
        // Test basic queries
        $testQueries = [
            "SELECT COUNT(*) as count FROM users" => "User count",
            "SELECT COUNT(*) as count FROM kelas" => "Kelas count", 
            "SELECT COUNT(*) as count FROM siswa" => "Siswa count",
            "SELECT COUNT(*) as count FROM catatan_siswa" => "Catatan count"
        ];
        
        foreach ($testQueries as $query => $description) {
            try {
                $result = $appPdo->query($query);
                $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
                echo "✅ $description: $count\n";
            } catch (Exception $e) {
                echo "❌ $description: ERROR - " . $e->getMessage() . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Koneksi aplikasi gagal: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "🎉 PERBAIKAN SELESAI!\n\n";
    
    echo "📋 LANGKAH SELANJUTNYA:\n";
    echo "1. Akses aplikasi di: http://localhost/siswa-app/public\n";
    echo "2. Login dengan:\n";
    echo "   - Username: admin\n";
    echo "   - Password: password\n";
    echo "3. Ubah password default setelah login\n";
    echo "4. Test semua fitur aplikasi\n";
    echo "5. Jika masih ada error, periksa log di logs/php_errors.log\n";
    
} catch (Exception $e) {
    echo "❌ FATAL ERROR: " . $e->getMessage() . "\n";
    echo "\nPastikan:\n";
    echo "1. MySQL server berjalan\n";
    echo "2. Kredensial database di app/config/db.php benar\n";
    echo "3. User database memiliki privilege CREATE, INSERT, UPDATE, DELETE\n";
}

echo "</pre>\n";

// Add some styling
echo "<style>
body { font-family: 'Courier New', monospace; background: #f5f5f5; margin: 20px; }
h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
pre { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
</style>";
?>
