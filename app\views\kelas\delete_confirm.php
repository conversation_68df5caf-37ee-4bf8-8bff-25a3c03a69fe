<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-exclamation-triangle text-danger"></i>
                        Konfirmasi Hapus Kelas
                    </h1>
                    <p class="text-muted mb-0">Pastikan Anda yakin ingin menghapus kelas ini</p>
                </div>
                <div>
                    <a href="/siswa-app/public/kelas" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Error!</strong> <?= htmlspecialchars($_SESSION['error']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['error']); endif; ?>

    <!-- Main Content -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i>
                        Peringatan: Hapus Kelas
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Warning Message -->
                    <div class="alert alert-warning" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle fa-2x text-warning mr-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">Perhatian!</h5>
                                <p class="mb-0">Tindakan ini akan menghapus kelas secara permanen dan tidak dapat dibatalkan.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Kelas Information -->
                    <div class="kelas-info mb-4">
                        <h6 class="text-muted mb-3">
                            <i class="bi bi-info-circle"></i>
                            Informasi Kelas yang akan dihapus:
                        </h6>
                        
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td><strong><i class="bi bi-tag"></i> Nama Kelas:</strong></td>
                                                <td><?= htmlspecialchars($kelas['nama_kelas']) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong><i class="bi bi-layers"></i> Tingkat:</strong></td>
                                                <td>
                                                    <?php
                                                    $tingkat = $kelas['tingkat'] ?? '';
                                                    switch($tingkat) {
                                                        case 'KPP':
                                                            $badgeClass = 'bg-info';
                                                            $tingkatLabel = 'KPP (Kelas Persiapan Pertama)';
                                                            break;
                                                        case 'X':
                                                            $badgeClass = 'bg-success';
                                                            $tingkatLabel = 'Kelas X (Kelas 10)';
                                                            break;
                                                        case 'XI':
                                                            $badgeClass = 'bg-warning';
                                                            $tingkatLabel = 'Kelas XI (Kelas 11)';
                                                            break;
                                                        case 'XII':
                                                            $badgeClass = 'bg-danger';
                                                            $tingkatLabel = 'Kelas XII (Kelas 12)';
                                                            break;
                                                        case 'KPA':
                                                            $badgeClass = 'bg-purple';
                                                            $tingkatLabel = 'KPA (Kelas Persiapan Atas)';
                                                            break;
                                                        default:
                                                            $badgeClass = 'bg-secondary';
                                                            $tingkatLabel = $tingkat ?: 'N/A';
                                                    }
                                                    ?>
                                                    <span class="badge <?= $badgeClass ?> text-white">
                                                        <?= $tingkatLabel ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong><i class="bi bi-book-half"></i> Kurikulum:</strong></td>
                                                <td>
                                                    <?php if (!empty($kelas['nama_kurikulum'])): ?>
                                                        <div class="d-flex align-items-center">
                                                            <span class="badge bg-primary text-white mr-2">
                                                                <?= htmlspecialchars($kelas['nama_kurikulum']) ?>
                                                            </span>
                                                            <small class="text-muted">
                                                                (<?= htmlspecialchars($kelas['kode_kurikulum']) ?>)
                                                            </small>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">Belum ada kurikulum</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td><strong><i class="bi bi-calendar"></i> Tahun Pelajaran:</strong></td>
                                                <td><?= htmlspecialchars($kelas['tahun_pelajaran']) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong><i class="bi bi-person"></i> Wali Kelas:</strong></td>
                                                <td><?= htmlspecialchars($kelas['wali_kelas'] ?? '-') ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong><i class="bi bi-people"></i> Kapasitas:</strong></td>
                                                <td>
                                                    <span class="badge bg-info text-white">
                                                        <?= htmlspecialchars($kelas['kapasitas'] ?? '30') ?> siswa
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Impact Warning -->
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading">
                            <i class="bi bi-exclamation-octagon"></i>
                            Dampak Penghapusan:
                        </h6>
                        <ul class="mb-0">
                            <li>Kelas akan dihapus secara permanen dari sistem</li>
                            <li>Data siswa yang terkait dengan kelas ini mungkin terpengaruh</li>
                            <li>Riwayat akademik yang terkait akan hilang</li>
                            <li>Tindakan ini <strong>TIDAK DAPAT DIBATALKAN</strong></li>
                        </ul>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="POST" action="/siswa-app/public/kelas/delete/<?= $kelas['id_kelas'] ?>" id="deleteForm">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label" for="confirmDelete">
                                    <strong>Saya memahami konsekuensi dan yakin ingin menghapus kelas ini</strong>
                                </label>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    <i class="bi bi-shield-check"></i>
                                    Tindakan ini memerlukan konfirmasi admin
                                </small>
                            </div>
                            <div>
                                <a href="/siswa-app/public/kelas" class="btn btn-secondary mr-2">
                                    <i class="bi bi-x-circle"></i>
                                    Batal
                                </a>
                                <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                    <i class="bi bi-trash"></i>
                                    Ya, Hapus Kelas
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
.bg-purple {
    background-color: #6f42c1 !important;
}

.card.border-danger {
    border-color: #dc3545 !important;
}

.kelas-info .table td {
    padding: 0.5rem 0.25rem;
    border: none;
    vertical-align: middle;
}

.kelas-info .table td:first-child {
    width: 40%;
    color: #495057;
    font-weight: 500;
}

.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.alert {
    border-radius: 0.5rem;
}

.alert-heading {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.fa-2x {
    font-size: 2em;
}

.form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .kelas-info .row {
        flex-direction: column;
    }
}
</style>

<!-- Custom JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    const deleteForm = document.getElementById('deleteForm');
    
    // Enable/disable delete button based on checkbox
    confirmCheckbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
        
        if (this.checked) {
            deleteButton.classList.remove('btn-secondary');
            deleteButton.classList.add('btn-danger');
        } else {
            deleteButton.classList.remove('btn-danger');
            deleteButton.classList.add('btn-secondary');
        }
    });
    
    // Form submission with final confirmation
    deleteForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!confirmCheckbox.checked) {
            alert('Silakan centang kotak konfirmasi terlebih dahulu.');
            return;
        }
        
        // Final confirmation dialog
        const kelasName = '<?= htmlspecialchars($kelas['nama_kelas']) ?>';
        const confirmed = confirm(
            `PERINGATAN TERAKHIR!\n\n` +
            `Anda akan menghapus kelas "${kelasName}" secara permanen.\n\n` +
            `Tindakan ini TIDAK DAPAT DIBATALKAN!\n\n` +
            `Apakah Anda benar-benar yakin?`
        );
        
        if (confirmed) {
            // Show loading state
            deleteButton.disabled = true;
            deleteButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menghapus...';
            
            // Submit form
            this.submit();
        }
    });
    
    // Auto-dismiss alerts
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert-danger');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-dismissible')) {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            }
        });
    }, 10000);
});
</script>
