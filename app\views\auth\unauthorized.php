<?php
// Set appropriate HTTP status code
http_response_code(403);
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="text-center">
                <div class="error-box">
                    <div class="error-icon">
                        <i class="bi bi-shield-exclamation text-danger"></i>
                    </div>
                    
                    <h1 class="error-title">403</h1>
                    <h2 class="error-subtitle"><PERSON><PERSON><PERSON></h2>
                    
                    <div class="error-description">
                        <p class="lead"><PERSON>af, Anda tidak memiliki izin untuk mengakses halaman ini.</p>
                        <p class="text-muted">
                            Halaman yang Anda coba akses memerlukan tingkat akses yang lebih tinggi 
                            atau role yang berbeda dari yang Anda miliki saat ini.
                        </p>
                    </div>

                    <div class="error-actions mt-4">
                        <a href="/siswa-app/public/dashboard" class="btn btn-primary me-2">
                            <i class="bi bi-house me-1"></i>
                            Kembali ke Dashboard
                        </a>
                        
                        <button onclick="history.back()" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            Halaman Sebelumnya
                        </button>
                    </div>

                    <div class="error-info mt-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Informasi Akun Anda
                                </h6>
                                <div class="row text-start">
                                    <div class="col-sm-6">
                                        <strong>Username:</strong><br>
                                        <span class="text-muted"><?= htmlspecialchars($_SESSION['username'] ?? 'Guest') ?></span>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Role:</strong><br>
                                        <span class="badge bg-<?= ($_SESSION['user_role'] ?? '') === 'admin' ? 'danger' : (($_SESSION['user_role'] ?? '') === 'guru' ? 'primary' : 'secondary') ?>">
                                            <?= ucfirst($_SESSION['user_role'] ?? 'Guest') ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="error-help mt-4">
                        <h6>Butuh bantuan?</h6>
                        <p class="text-muted small">
                            Jika Anda merasa ini adalah kesalahan, silakan hubungi administrator sistem 
                            atau pastikan Anda telah login dengan akun yang memiliki izin yang sesuai.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-box {
    padding: 2rem 0;
}

.error-icon {
    font-size: 5rem;
    margin-bottom: 1rem;
}

.error-title {
    font-size: 6rem;
    font-weight: 700;
    color: #dc3545;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-subtitle {
    font-size: 2rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1.5rem;
}

.error-description {
    margin-bottom: 2rem;
}

.error-actions .btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

.error-info .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.error-help {
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}

/* Animation */
.error-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .error-title {
        font-size: 4rem;
    }
    
    .error-subtitle {
        font-size: 1.5rem;
    }
    
    .error-icon {
        font-size: 4rem;
    }
    
    .error-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .error-actions .btn:last-child {
        margin-bottom: 0;
    }
}
</style>

<script>
// Auto redirect after 30 seconds (optional)
// setTimeout(function() {
//     window.location.href = '/siswa-app/public/dashboard';
// }, 30000);

// Log unauthorized access attempt
console.warn('Unauthorized access attempt detected');

// Optional: Send analytics or logging data
document.addEventListener('DOMContentLoaded', function() {
    // You can add analytics tracking here
    // Example: gtag('event', 'unauthorized_access', { page_location: window.location.href });
});
</script>
