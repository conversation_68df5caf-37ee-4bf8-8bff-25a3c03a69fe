-- <PERSON><PERSON>t untuk <PERSON> Tabel yang <PERSON>
-- Sistem Informasi Akademik Siswa
-- Jalankan script ini untuk menambahkan tabel siswa, kelas, berkas, dan audit_logs

USE siswa_app;

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Table: kelas (harus dibuat dulu karena siswa referensi ke kelas)
CREATE TABLE IF NOT EXISTS kelas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama_kelas VARCHAR(20) NOT NULL,
    tingkat INT NOT NULL,
    jurusan VARCHAR(50),
    tahun_pelajaran VARCHAR(9) NOT NULL,
    wali_kelas VARCHAR(100),
    kapasitas INT DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_tingkat (tingkat),
    INDEX idx_tahun (tahun_pelajaran),
    INDEX idx_active (is_active)
);

-- Table: siswa (referensi ke kelas)
CREATE TABLE IF NOT EXISTS siswa (
    id_siswa INT PRIMARY KEY AUTO_INCREMENT,
    nis VARCHAR(20) UNIQUE NOT NULL,
    nisn VARCHAR(20) UNIQUE,
    nama_lengkap VARCHAR(100) NOT NULL,
    jenis_kelamin ENUM('L', 'P') NOT NULL,
    tempat_lahir VARCHAR(50),
    tanggal_lahir DATE,
    alamat TEXT,
    no_telepon VARCHAR(20),
    email VARCHAR(100),
    nama_ayah VARCHAR(100),
    nama_ibu VARCHAR(100),
    pekerjaan_ayah VARCHAR(50),
    pekerjaan_ibu VARCHAR(50),
    kelas_id INT,
    tahun_masuk YEAR,
    status_siswa ENUM('aktif', 'lulus', 'mutasi', 'dropout') DEFAULT 'aktif',
    foto VARCHAR(255),
    created_by INT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (kelas_id) REFERENCES kelas(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_nis (nis),
    INDEX idx_nama (nama_lengkap),
    INDEX idx_status (status_siswa),
    INDEX idx_kelas (kelas_id)
);

-- Table: berkas (referensi ke siswa)
CREATE TABLE IF NOT EXISTS berkas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    siswa_id INT NOT NULL,
    jenis_berkas ENUM(
        'kartu_keluarga', 'akta_lahir',
        'rapor_kelas_x', 'rapor_kelas_xi', 'rapor_kelas_xii',
        'ijazah_sd', 'ijazah_smp', 'ijazah_sma',
        'foto_siswa', 'surat_keterangan_sehat', 'surat_kelakuan_baik',
        'piagam_prestasi', 'lainnya'
    ) NOT NULL,
    nama_berkas VARCHAR(255) NOT NULL,
    nama_file_asli VARCHAR(255) NOT NULL,
    nama_file_sistem VARCHAR(255) NOT NULL,
    ukuran_file INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash untuk integrity check
    keterangan TEXT,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (siswa_id) REFERENCES siswa(id_siswa) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    INDEX idx_siswa (siswa_id),
    INDEX idx_jenis (jenis_berkas),
    INDEX idx_hash (file_hash)
);

-- Table: audit_logs (untuk security audit)
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_table (table_name),
    INDEX idx_created (created_at)
);

-- Table: kategori_catatan (untuk kategorisasi catatan)
CREATE TABLE IF NOT EXISTS kategori_catatan (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama_kategori VARCHAR(100) NOT NULL,
    kode_kategori VARCHAR(20) UNIQUE NOT NULL,
    deskripsi TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_kode (kode_kategori),
    INDEX idx_active (is_active)
);

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Insert sample classes
INSERT IGNORE INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, created_by) VALUES 
('X-IPA-1', 10, 'IPA', '2024/2025', 'Budi Santoso', 1),
('X-IPS-1', 10, 'IPS', '2024/2025', 'Siti Aminah', 1),
('XI-IPA-1', 11, 'IPA', '2024/2025', 'Ahmad Rahman', 1),
('XI-IPS-1', 11, 'IPS', '2024/2025', 'Dewi Sartika', 1),
('XII-IPA-1', 12, 'IPA', '2024/2025', 'Joko Widodo', 1),
('XII-IPS-1', 12, 'IPS', '2024/2025', 'Susi Susanti', 1);

-- Insert sample kategori catatan
INSERT IGNORE INTO kategori_catatan (nama_kategori, kode_kategori, deskripsi) VALUES
('Akademik', 'akademik', 'Catatan terkait prestasi dan masalah akademik'),
('Prestasi', 'prestasi', 'Catatan prestasi siswa'),
('Pelanggaran', 'pelanggaran', 'Catatan pelanggaran dan tindakan disiplin'),
('Kesehatan', 'kesehatan', 'Catatan kesehatan siswa'),
('Kehadiran', 'kehadiran', 'Catatan kehadiran dan ketidakhadiran'),
('Umum', 'umum', 'Catatan umum lainnya');

-- Insert sample students
INSERT IGNORE INTO siswa (nis, nisn, nama_lengkap, jenis_kelamin, tempat_lahir, tanggal_lahir, alamat, kelas_id, tahun_masuk, created_by) VALUES
('2024001', '1234567890', 'Ahmad Fauzi', 'L', 'Jakarta', '2007-05-15', 'Jl. Merdeka No. 123, Jakarta', 1, 2024, 1),
('2024002', '1234567891', 'Siti Nurhaliza', 'P', 'Bandung', '2007-08-20', 'Jl. Sudirman No. 456, Bandung', 1, 2024, 1),
('2024003', '1234567892', 'Budi Santoso', 'L', 'Surabaya', '2007-03-10', 'Jl. Pahlawan No. 789, Surabaya', 2, 2024, 1),
('2023001', '1234567893', 'Dewi Sartika', 'P', 'Yogyakarta', '2006-12-05', 'Jl. Malioboro No. 321, Yogyakarta', 3, 2023, 1),
('2023002', '1234567894', 'Rudi Hermawan', 'L', 'Medan', '2006-09-18', 'Jl. Gatot Subroto No. 654, Medan', 4, 2023, 1);

-- Verify tables created
SELECT 'Tables created successfully!' as status;
SELECT TABLE_NAME, TABLE_ROWS 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'siswa_app' 
ORDER BY TABLE_NAME;
