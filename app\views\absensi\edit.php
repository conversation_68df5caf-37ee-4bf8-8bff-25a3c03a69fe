<?php
require_once __DIR__ . '/../layouts/header.php';
require_once __DIR__ . '/../components/elegant-alerts.php';
?>

<div class="container-fluid py-4">
    <!-- Enhanced Alert Messages -->
    <?= showSessionAlerts() ?>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/siswa-app/public/dashboard">
                    <i class="bi bi-house"></i> Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="/siswa-app/public/siswa/detail/<?= $absensi['siswa_id'] ?? '' ?>">
                    <i class="bi bi-person"></i> Detail Siswa
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="bi bi-calendar-check"></i> Edit Absensi
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-calendar-check text-primary"></i>
                        Edit Absensi
                    </h1>
                    <p class="text-muted mb-0">
                        Edit data kehadiran siswa
                    </p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa/detail/<?= $absensi['siswa_id'] ?? '' ?>" 
                       class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pencil-square"></i>
                        Form Edit Absensi
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/siswa-app/public/absensi/update/<?= $absensi['id'] ?? '' ?>">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <!-- Siswa Info -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Nama Siswa</label>
                                <input type="text" class="form-control" 
                                       value="<?= htmlspecialchars($siswa['nama_lengkap'] ?? '') ?>" 
                                       readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Kelas</label>
                                <input type="text" class="form-control" 
                                       value="<?= htmlspecialchars($siswa['nama_kelas'] ?? '') ?>" 
                                       readonly>
                            </div>
                        </div>

                        <!-- Tanggal -->
                        <div class="mb-3">
                            <label for="tanggal" class="form-label">
                                <i class="bi bi-calendar"></i> Tanggal
                            </label>
                            <input type="date" class="form-control" id="tanggal" name="tanggal" 
                                   value="<?= $absensi['tanggal'] ?? date('Y-m-d') ?>" required>
                        </div>

                        <!-- Status Kehadiran -->
                        <div class="mb-3">
                            <label for="status_kehadiran" class="form-label">
                                <i class="bi bi-check-circle"></i> Status Kehadiran
                            </label>
                            <select class="form-select" id="status_kehadiran" name="status_kehadiran" required>
                                <option value="">Pilih Status</option>
                                <option value="hadir" <?= ($absensi['status_kehadiran'] ?? '') === 'hadir' ? 'selected' : '' ?>>
                                    ✅ Hadir
                                </option>
                                <option value="sakit" <?= ($absensi['status_kehadiran'] ?? '') === 'sakit' ? 'selected' : '' ?>>
                                    🤒 Sakit
                                </option>
                                <option value="ijin" <?= ($absensi['status_kehadiran'] ?? '') === 'ijin' ? 'selected' : '' ?>>
                                    📝 Ijin
                                </option>
                                <option value="alpha" <?= ($absensi['status_kehadiran'] ?? '') === 'alpha' ? 'selected' : '' ?>>
                                    ❌ Alpha
                                </option>
                            </select>
                        </div>

                        <!-- Keterangan -->
                        <div class="mb-3">
                            <label for="keterangan" class="form-label">
                                <i class="bi bi-chat-text"></i> Keterangan
                            </label>
                            <textarea class="form-control" id="keterangan" name="keterangan" 
                                      rows="3" placeholder="Keterangan tambahan (opsional)"><?= htmlspecialchars($absensi['keterangan'] ?? '') ?></textarea>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="/siswa-app/public/siswa/detail/<?= $absensi['siswa_id'] ?? '' ?>" 
                               class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Absensi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Alert Styles -->
<?= getAlertStyles() ?>
<?= getAlertScripts() ?>

<style>
.card {
    border: none;
    border-radius: 12px;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
}

.breadcrumb {
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #0d6efd;
}

.breadcrumb-item.active {
    color: #495057;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textarea
    const textarea = document.getElementById('keterangan');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const tanggal = document.getElementById('tanggal').value;
        const status = document.getElementById('status_kehadiran').value;
        
        if (!tanggal || !status) {
            e.preventDefault();
            showNotification('error', 'Validasi Error', 'Tanggal dan status kehadiran harus diisi!');
            return false;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Menyimpan...';
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
